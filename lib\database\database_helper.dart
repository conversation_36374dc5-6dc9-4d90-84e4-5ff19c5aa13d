import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/card_stock.dart';
import '../models/card_profit.dart';
import '../models/card_inventory.dart';
import '../models/custom_card_type.dart';

class DatabaseHelper {
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();
  static final DatabaseHelper _instance = DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final String path = join(await getDatabasesPath(), 'debt_accountant.db');

    // لا نحذف قاعدة البيانات - نتركها تعمل بشكل طبيعي
    debugPrint('Opening database at: $path');

    return openDatabase(
      path,
      version: 13, // زيادة رقم الإصدار لإصلاح المشاكل
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onOpen: (db) async {
        // Enable foreign key constraints
        await db.execute('PRAGMA foreign_keys = ON');
        debugPrint('Database opened successfully');
      },
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Recreate customers table without email and address columns
      await db.execute('ALTER TABLE customers RENAME TO customers_old');

      await db.execute('''
        CREATE TABLE customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      await db.execute('''
        INSERT INTO customers (id, name, phone, created_at, updated_at)
        SELECT id, name, COALESCE(phone, ''), created_at, updated_at FROM customers_old
      ''');

      await db.execute('DROP TABLE customers_old');
    }

    if (oldVersion < 3) {
      // Fix any null phone values
      await db.execute('UPDATE customers SET phone = NULL WHERE phone = ""');
    }

    if (oldVersion < 4) {
      // Force refresh for UI changes
      // Database updated to version 4 - UI changes applied
    }

    if (oldVersion < 7) {
      // Create custom card types table
      await db.execute('''
        CREATE TABLE custom_card_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          display_name TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // Create index for better performance
      await db.execute(
        'CREATE INDEX idx_custom_card_types_name ON custom_card_types (name)',
      );
    }

    if (oldVersion < 9) {
      // Create card profits table
      await db.execute('''
        CREATE TABLE card_profits (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          card_type TEXT NOT NULL,
          cost_price REAL NOT NULL,
          selling_price REAL NOT NULL,
          profit REAL NOT NULL,
          quantity INTEGER NOT NULL,
          total_profit REAL NOT NULL,
          date TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
    }

    if (oldVersion < 10) {
      // Create card inventories table
      await db.execute('''
        CREATE TABLE card_inventories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          card_type TEXT NOT NULL UNIQUE,
          quantity INTEGER NOT NULL DEFAULT 0,
          min_quantity INTEGER NOT NULL DEFAULT 10,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
    }

    if (oldVersion < 12) {
      // Force database refresh to fix any column issues
      debugPrint('Database upgraded to version 12 - fixing column issues');
    }

    if (oldVersion < 13) {
      // إصلاح مشاكل قاعدة البيانات وإضافة جدول card_inventories إذا لم يكن موجوداً
      debugPrint('Upgrading to version 13 - fixing database issues');

      try {
        // التحقق من وجود جدول card_inventories
        final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='card_inventories'",
        );

        if (result.isEmpty) {
          // إنشاء جدول card_inventories
          await db.execute('''
            CREATE TABLE card_inventories (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              card_type TEXT NOT NULL UNIQUE,
              quantity INTEGER NOT NULL DEFAULT 0,
              min_quantity INTEGER NOT NULL DEFAULT 10,
              created_at TEXT NOT NULL,
              updated_at TEXT NOT NULL
            )
          ''');

          // إنشاء فهرس للأداء
          await db.execute(
            'CREATE INDEX idx_card_inventories_card_type ON card_inventories (card_type)',
          );

          debugPrint('Created card_inventories table successfully');
        } else {
          debugPrint('card_inventories table already exists');
        }
      } catch (e) {
        debugPrint('Error creating card_inventories table: $e');
      }
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create customers table
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create debts table
    await db.execute('''
      CREATE TABLE debts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0.0,
        card_type TEXT NOT NULL,
        notes TEXT,
        entry_date INTEGER NOT NULL,
        due_date INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        status INTEGER DEFAULT 0,
        first_payment_date INTEGER,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Create payments table
    await db.execute('''
      CREATE TABLE payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        debt_id INTEGER NOT NULL,
        customer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        type INTEGER NOT NULL,
        notes TEXT,
        payment_date INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (debt_id) REFERENCES debts (id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Create custom card types table
    await db.execute('''
      CREATE TABLE custom_card_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        display_name TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create card stocks table
    await db.execute('''
      CREATE TABLE card_stocks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        card_type_id TEXT NOT NULL UNIQUE,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER NOT NULL DEFAULT 10,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create card profits table
    await db.execute('''
      CREATE TABLE card_profits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        card_type TEXT NOT NULL,
        cost_price REAL NOT NULL,
        selling_price REAL NOT NULL,
        profit REAL NOT NULL,
        quantity INTEGER NOT NULL,
        total_profit REAL NOT NULL,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create card inventories table
    await db.execute('''
      CREATE TABLE card_inventories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        card_type TEXT NOT NULL UNIQUE,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER NOT NULL DEFAULT 10,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute(
      'CREATE INDEX idx_debts_customer_id ON debts (customer_id)',
    );
    await db.execute('CREATE INDEX idx_debts_status ON debts (status)');
    await db.execute('CREATE INDEX idx_payments_debt_id ON payments (debt_id)');
    await db.execute(
      'CREATE INDEX idx_payments_customer_id ON payments (customer_id)',
    );
    await db.execute(
      'CREATE INDEX idx_card_stocks_card_type ON card_stocks (card_type_id)',
    );
    await db.execute(
      'CREATE INDEX idx_card_inventories_card_type ON card_inventories (card_type)',
    );
  }

  // Customer CRUD operations
  Future<int> insertCustomer(Customer customer) async {
    final db = await database;
    return db.insert('customers', customer.toMap());
  }

  Future<List<Customer>> getAllCustomers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  Future<Customer?> getCustomer(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCustomer(Customer customer) async {
    final db = await database;
    return db.update(
      'customers',
      customer.toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  Future<int> deleteCustomer(int id) async {
    final db = await database;

    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');

    // Start transaction to ensure data consistency
    return db.transaction((txn) async {
      // First delete all payments for this customer
      await txn.delete('payments', where: 'customer_id = ?', whereArgs: [id]);

      // Then delete all debts for this customer
      await txn.delete('debts', where: 'customer_id = ?', whereArgs: [id]);

      // Finally delete the customer
      return txn.delete('customers', where: 'id = ?', whereArgs: [id]);
    });
  }

  Future<List<Customer>> searchCustomers(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'name LIKE ? OR phone LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  // Debt CRUD operations
  Future<int> insertDebt(Debt debt) async {
    final db = await database;
    return db.insert('debts', debt.toMap());
  }

  Future<List<Debt>> getAllDebts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<List<Debt>> getCustomerDebts(int customerId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<List<Debt>> getPendingDebts(int customerId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'customer_id = ? AND status = ?',
      whereArgs: [customerId, DebtStatus.pending.index],
      orderBy: 'due_date ASC',
    );
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<Debt?> getDebt(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Debt.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateDebt(Debt debt) async {
    final db = await database;
    return db.update(
      'debts',
      debt.toMap(),
      where: 'id = ?',
      whereArgs: [debt.id],
    );
  }

  Future<int> deleteDebt(int id) async {
    final db = await database;
    return db.delete('debts', where: 'id = ?', whereArgs: [id]);
  }

  // Payment CRUD operations
  Future<int> insertPayment(Payment payment) async {
    final db = await database;
    debugPrint(
      'DatabaseHelper: Inserting payment - Customer: ${payment.customerId}, Debt: ${payment.debtId}, Amount: ${payment.amount}, Type: ${payment.type}',
    );
    final id = await db.insert('payments', payment.toMap());
    debugPrint('DatabaseHelper: Payment inserted with ID: $id');
    return id;
  }

  Future<List<Payment>> getCustomerPayments(int customerId) async {
    final db = await database;
    debugPrint('DatabaseHelper: Loading payments for customer $customerId');
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'payment_date DESC',
    );
    debugPrint(
      'DatabaseHelper: Found ${maps.length} payments for customer $customerId',
    );
    if (maps.isNotEmpty) {
      debugPrint(
        'DatabaseHelper: Payment details: ${maps.map((m) => 'ID:${m['id']}, Amount:${m['amount']}, Type:${m['type']}').join(', ')}',
      );
    }
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<List<Payment>> getDebtPayments(int debtId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'debt_id = ?',
      whereArgs: [debtId],
      orderBy: 'payment_date DESC',
    );
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<int> updatePayment(Payment payment) async {
    final db = await database;
    return db.update(
      'payments',
      payment.toMap(),
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  Future<int> deletePayment(int id) async {
    final db = await database;
    return db.delete('payments', where: 'id = ?', whereArgs: [id]);
  }

  // Statistics
  Future<Map<String, dynamic>> getStatistics() async {
    final db = await database;

    // Total customers
    final customerCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM customers'),
        ) ??
        0;

    // Total debts amount
    final totalDebts =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT SUM(amount) FROM debts WHERE status != ?', [
            DebtStatus.paid.index,
          ]),
        ) ??
        0;

    // Total paid amount
    final totalPaid =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT SUM(amount) FROM payments'),
        ) ??
        0;

    // Overdue debts
    final overdueDebts =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM debts WHERE due_date < ? AND status != ?',
            [DateTime.now().millisecondsSinceEpoch, DebtStatus.paid.index],
          ),
        ) ??
        0;

    // Get paid debts count
    final paidDebts =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM debts WHERE status = ?', [
            DebtStatus.paid.index,
          ]),
        ) ??
        0;

    // Get pending debts count
    final pendingDebts =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM debts WHERE status = ?', [
            DebtStatus.pending.index,
          ]),
        ) ??
        0;

    // Get total debts count
    final totalDebtsCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM debts'),
        ) ??
        0;

    return {
      'totalCustomers': customerCount,
      'totalDebts': totalDebts,
      'totalPaid': totalPaid,
      'overdueDebts': overdueDebts,
      'paidDebts': paidDebts,
      'pendingDebts': pendingDebts,
      'totalDebtsCount': totalDebtsCount,
      'remainingAmount': totalDebts - totalPaid,
    };
  }

  // إحصائيات المبيعات حسب نوع الكارت
  Future<List<Map<String, dynamic>>> getSalesByCardType() async {
    final db = await database;

    final result = await db.rawQuery('''
      SELECT
        d.card_type,
        COUNT(*) as count,
        SUM(d.quantity) as total_quantity,
        SUM(d.amount) as total_amount,
        AVG(d.amount) as avg_amount
      FROM debts d
      GROUP BY d.card_type
      ORDER BY total_amount DESC
    ''');

    return result;
  }

  // إحصائيات المبيعات الشهرية
  Future<List<Map<String, dynamic>>> getMonthlySales() async {
    final db = await database;

    final result = await db.rawQuery(
      '''
      SELECT
        strftime('%Y-%m', datetime(d.created_at/1000, 'unixepoch')) as month,
        COUNT(*) as count,
        SUM(d.amount) as total_amount,
        SUM(d.quantity) as total_quantity
      FROM debts d
      WHERE d.created_at >= ?
      GROUP BY strftime('%Y-%m', datetime(d.created_at/1000, 'unixepoch'))
      ORDER BY month DESC
    ''',
      [
        DateTime.now()
            .subtract(const Duration(days: 365))
            .millisecondsSinceEpoch,
      ],
    );

    return result;
  }

  // أفضل العملاء (حسب إجمالي المبالغ)
  Future<List<Map<String, dynamic>>> getTopCustomers({int limit = 10}) async {
    final db = await database;

    final result = await db.rawQuery(
      '''
      SELECT
        c.id,
        c.name,
        c.phone,
        COUNT(d.id) as debts_count,
        SUM(d.amount) as total_amount,
        SUM(CASE WHEN d.status = ? THEN d.amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN d.status = ? THEN d.amount ELSE 0 END) as pending_amount
      FROM customers c
      LEFT JOIN debts d ON c.id = d.customer_id
      GROUP BY c.id, c.name, c.phone
      HAVING total_amount > 0
      ORDER BY total_amount DESC
      LIMIT ?
    ''',
      [DebtStatus.paid.index, DebtStatus.pending.index, limit],
    );

    return result;
  }

  // إحصائيات المخزون
  Future<Map<String, dynamic>> getStockStatistics() async {
    final db = await database;

    try {
      // إجمالي أنواع البطاقات في المخزون
      final totalCardTypes =
          Sqflite.firstIntValue(
            await db.rawQuery(
              'SELECT COUNT(DISTINCT card_type) FROM card_inventories',
            ),
          ) ??
          0;

      // إجمالي الكمية في المخزون
      final totalQuantity =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT SUM(quantity) FROM card_inventories'),
          ) ??
          0;

      // البطاقات المنخفضة المخزون (أقل من 10)
      final lowStockCount =
          Sqflite.firstIntValue(
            await db.rawQuery(
              'SELECT COUNT(DISTINCT card_type) FROM card_inventories WHERE quantity <= 10 AND quantity > 0',
            ),
          ) ??
          0;

      // البطاقات النافدة
      final outOfStockCount =
          Sqflite.firstIntValue(
            await db.rawQuery(
              'SELECT COUNT(DISTINCT card_type) FROM card_inventories WHERE quantity = 0',
            ),
          ) ??
          0;

      return {
        'totalCardTypes': totalCardTypes,
        'totalQuantity': totalQuantity,
        'lowStockCount': lowStockCount,
        'outOfStockCount': outOfStockCount,
      };
    } catch (e) {
      debugPrint('Error getting stock statistics: $e');
      return {
        'totalCardTypes': 0,
        'totalQuantity': 0,
        'lowStockCount': 0,
        'outOfStockCount': 0,
      };
    }
  }

  // Card Stock CRUD operations
  Future<int> insertCardStock(CardStock cardStock) async {
    final db = await database;
    return db.insert('card_stocks', cardStock.toMap());
  }

  Future<List<CardStock>> getAllCardStocks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_stocks',
      orderBy: 'card_type_id ASC',
    );
    return List.generate(maps.length, (i) => CardStock.fromMap(maps[i]));
  }

  // الحصول على مخزون البطاقة - بسيط وفعال
  Future<CardStock?> getCardStock(String cardTypeId) async {
    final db = await database;

    // البحث المباشر
    final List<Map<String, dynamic>> maps = await db.query(
      'card_stocks',
      where: 'card_type_id = ?',
      whereArgs: [cardTypeId],
    );

    if (maps.isNotEmpty) {
      return CardStock.fromMap(maps.first);
    }

    return null;
  }

  // ==================== Card Profits Operations ====================

  // إدراج ربح جديد
  Future<int> insertCardProfit(CardProfit profit) async {
    final db = await database;
    return db.insert('card_profits', profit.toMap());
  }

  // الحصول على جميع الأرباح
  Future<List<CardProfit>> getAllCardProfits() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_profits',
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => CardProfit.fromMap(maps[i]));
  }

  // تحديث ربح
  Future<int> updateCardProfit(CardProfit profit) async {
    final db = await database;
    return db.update(
      'card_profits',
      profit.toMap(),
      where: 'id = ?',
      whereArgs: [profit.id],
    );
  }

  // حذف ربح
  Future<int> deleteCardProfit(int id) async {
    final db = await database;
    return db.delete('card_profits', where: 'id = ?', whereArgs: [id]);
  }

  // الحصول على أرباح نوع بطاقة معين
  Future<List<CardProfit>> getProfitsByCardType(String cardType) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_profits',
      where: 'card_type = ?',
      whereArgs: [cardType],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => CardProfit.fromMap(maps[i]));
  }

  // ==================== Card Inventories Operations ====================

  // إدراج كمية جديدة
  Future<int> insertCardInventory(CardInventory inventory) async {
    final db = await database;
    return db.insert('card_inventories', inventory.toMap());
  }

  // الحصول على جميع الكميات
  Future<List<CardInventory>> getAllCardInventories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_inventories',
      orderBy: 'card_type ASC',
    );
    return List.generate(maps.length, (i) => CardInventory.fromMap(maps[i]));
  }

  // تحديث كمية
  Future<int> updateCardInventory(CardInventory inventory) async {
    final db = await database;
    return db.update(
      'card_inventories',
      inventory.toMap(),
      where: 'id = ?',
      whereArgs: [inventory.id],
    );
  }

  // حذف كمية
  Future<int> deleteCardInventory(int id) async {
    final db = await database;
    return db.delete('card_inventories', where: 'id = ?', whereArgs: [id]);
  }

  // الحصول على كمية نوع بطاقة معين
  Future<CardInventory?> getCardInventoryByType(String cardType) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'card_inventories',
      where: 'card_type = ?',
      whereArgs: [cardType],
    );

    if (maps.isNotEmpty) {
      return CardInventory.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCardStock(CardStock cardStock) async {
    final db = await database;
    return db.update(
      'card_stocks',
      cardStock.toMap(),
      where: 'id = ?',
      whereArgs: [cardStock.id],
    );
  }

  Future<int> deleteCardStock(int id) async {
    final db = await database;
    return db.delete('card_stocks', where: 'id = ?', whereArgs: [id]);
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }

  // Database maintenance and cleanup functions
  Future<void> cleanupDatabase() async {
    final db = await database;

    // Remove orphaned payments (payments without corresponding debts)
    await db.execute('''
      DELETE FROM payments
      WHERE debt_id NOT IN (SELECT id FROM debts)
    ''');

    // Remove orphaned debts (debts without corresponding customers)
    await db.execute('''
      DELETE FROM debts
      WHERE customer_id NOT IN (SELECT id FROM customers)
    ''');

    // Vacuum database to reclaim space
    await db.execute('VACUUM');
  }

  // Get database statistics
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;

    final customerCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM customers'),
        ) ??
        0;

    final debtCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM debts'),
        ) ??
        0;

    final paymentCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM payments'),
        ) ??
        0;

    final customCardTypeCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM custom_card_types'),
        ) ??
        0;

    // إحصائيات المخزون مع معالجة الأخطاء
    int inventoryCount = 0;
    try {
      inventoryCount =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM card_inventories'),
          ) ??
          0;
    } catch (e) {
      debugPrint('Error getting inventory count: $e');
    }

    return {
      'version': await db.getVersion(),
      'customers': customerCount,
      'debts': debtCount,
      'payments': paymentCount,
      'inventory': inventoryCount,
      'customCardTypes': customCardTypeCount,
    };
  }

  // CustomCardType CRUD operations
  Future<int> insertCustomCardType(CustomCardType customCardType) async {
    final db = await database;
    return db.insert('custom_card_types', customCardType.toMap());
  }

  Future<List<CustomCardType>> getAllCustomCardTypes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'custom_card_types',
      orderBy: 'display_name ASC',
    );
    return List.generate(maps.length, (i) => CustomCardType.fromMap(maps[i]));
  }

  Future<int> updateCustomCardType(CustomCardType customCardType) async {
    final db = await database;
    return db.update(
      'custom_card_types',
      customCardType.toMap(),
      where: 'id = ?',
      whereArgs: [customCardType.id],
    );
  }

  Future<int> deleteCustomCardType(int id) async {
    final db = await database;
    return db.delete('custom_card_types', where: 'id = ?', whereArgs: [id]);
  }

  // Debug function to check customer data
  Future<Map<String, dynamic>> debugCustomerData(int customerId) async {
    final db = await database;

    // Get customer info
    final customerResult = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [customerId],
    );

    // Get all debts for this customer
    final debtsResult = await db.query(
      'debts',
      where: 'customer_id = ?',
      whereArgs: [customerId],
    );

    // Get all payments for this customer
    final paymentsResult = await db.query(
      'payments',
      where: 'customer_id = ?',
      whereArgs: [customerId],
    );

    return {
      'customer': customerResult,
      'debts': debtsResult,
      'payments': paymentsResult,
      'debts_count': debtsResult.length,
      'payments_count': paymentsResult.length,
    };
  }

  // Comprehensive Reports
  Future<Map<String, dynamic>> getComprehensiveReports() async {
    final db = await database;

    try {
      // Total Revenue
      final totalRevenueResult = await db.rawQuery(
        'SELECT SUM(amount) as total FROM payments',
      );
      final totalRevenue =
          (totalRevenueResult.first['total'] as num?)?.toDouble() ?? 0.0;

      // Total Debts Count
      final totalDebtsResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM debts',
      );
      final totalDebts = totalDebtsResult.first['count'] as int? ?? 0;

      // Total Customers Count
      final totalCustomersResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM customers',
      );
      final totalCustomers = totalCustomersResult.first['count'] as int? ?? 0;

      // Collection Rate (percentage of paid debts)
      final paidDebtsResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM debts WHERE status = ?',
        [DebtStatus.paid.index],
      );
      final paidDebts = paidDebtsResult.first['count'] as int? ?? 0;
      final collectionRate = totalDebts > 0
          ? (paidDebts / totalDebts) * 100
          : 0.0;

      // Top Customers by total amount
      final topCustomersResult = await db.rawQuery('''
        SELECT c.name, c.id, SUM(p.amount) as total_amount, COUNT(d.id) as total_debts
        FROM customers c
        LEFT JOIN debts d ON c.id = d.customer_id
        LEFT JOIN payments p ON d.id = p.debt_id
        GROUP BY c.id, c.name
        HAVING total_amount > 0
        ORDER BY total_amount DESC
        LIMIT 10
      ''');

      final topCustomers = topCustomersResult
          .map(
            (row) => {
              'name': row['name'] as String? ?? 'غير محدد',
              'totalAmount': (row['total_amount'] as num?)?.toDouble() ?? 0.0,
              'totalDebts': row['total_debts'] as int? ?? 0,
            },
          )
          .toList();

      // Monthly revenue trends (last 6 months)
      final now = DateTime.now();
      final monthlyRevenue = <Map<String, dynamic>>[];

      for (int i = 5; i >= 0; i--) {
        final month = DateTime(now.year, now.month - i);
        final nextMonth = DateTime(now.year, now.month - i + 1);

        final monthRevenueResult = await db.rawQuery(
          '''
          SELECT SUM(amount) as total
          FROM payments
          WHERE payment_date >= ? AND payment_date < ?
        ''',
          [month.millisecondsSinceEpoch, nextMonth.millisecondsSinceEpoch],
        );

        final monthRevenue =
            (monthRevenueResult.first['total'] as num?)?.toDouble() ?? 0.0;

        monthlyRevenue.add({
          'month': month.month,
          'year': month.year,
          'revenue': monthRevenue,
        });
      }

      // Card types distribution
      final cardTypesResult = await db.rawQuery('''
        SELECT card_type, COUNT(*) as count, SUM(amount) as total_amount
        FROM debts
        GROUP BY card_type
        ORDER BY count DESC
      ''');

      final cardTypesDistribution = cardTypesResult
          .map(
            (row) => {
              'cardType': row['card_type'] as String? ?? 'غير محدد',
              'count': row['count'] as int? ?? 0,
              'totalAmount': (row['total_amount'] as num?)?.toDouble() ?? 0.0,
            },
          )
          .toList();

      // Payment methods analysis
      final paymentMethodsResult = await db.rawQuery('''
        SELECT type, COUNT(*) as count, SUM(amount) as total_amount
        FROM payments
        GROUP BY type
        ORDER BY count DESC
      ''');

      final paymentMethods = paymentMethodsResult
          .map(
            (row) => {
              'type': row['type'] as int? ?? 0,
              'count': row['count'] as int? ?? 0,
              'totalAmount': (row['total_amount'] as num?)?.toDouble() ?? 0.0,
            },
          )
          .toList();

      return {
        'totalRevenue': totalRevenue,
        'totalDebts': totalDebts,
        'totalCustomers': totalCustomers,
        'collectionRate': collectionRate,
        'topCustomers': topCustomers,
        'monthlyRevenue': monthlyRevenue,
        'cardTypesDistribution': cardTypesDistribution,
        'paymentMethods': paymentMethods,
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error generating comprehensive reports: $e');
      return {
        'totalRevenue': 0.0,
        'totalDebts': 0,
        'totalCustomers': 0,
        'collectionRate': 0.0,
        'topCustomers': <Map<String, dynamic>>[],
        'monthlyRevenue': <Map<String, dynamic>>[],
        'cardTypesDistribution': <Map<String, dynamic>>[],
        'paymentMethods': <Map<String, dynamic>>[],
        'generatedAt': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }
}
