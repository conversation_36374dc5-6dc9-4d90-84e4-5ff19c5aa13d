import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/card_profit_provider.dart';
import '../providers/card_type_provider.dart';
import '../models/card_profit.dart';
import '../utils/number_formatter.dart';

class CardProfitScreen extends StatefulWidget {
  const CardProfitScreen({super.key});

  @override
  State<CardProfitScreen> createState() => _CardProfitScreenState();
}

class _CardProfitScreenState extends State<CardProfitScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CardProfitProvider>(context, listen: false).loadProfits();
      Provider.of<CardTypeProvider>(
        context,
        listen: false,
      ).loadCustomCardTypes();
      _addSampleDataIfEmpty();
    });
  }

  // إضافة بيانات تجريبية إذا كانت قائمة الأرباح فارغة
  void _addSampleDataIfEmpty() async {
    final profitProvider = Provider.of<CardProfitProvider>(
      context,
      listen: false,
    );

    // انتظار تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    if (profitProvider.profits.isEmpty) {
      // إضافة بيانات تجريبية
      final sampleProfits = [
        CardProfit(
          cardType: 'زين',
          costPrice: 6500,
          sellingPrice: 6750,
          quantity: 10,
          date: DateTime.now().subtract(const Duration(days: 1)),
        ),
        CardProfit(
          cardType: 'آسيا',
          costPrice: 7500,
          sellingPrice: 7750,
          quantity: 5,
          date: DateTime.now().subtract(const Duration(days: 2)),
        ),
        CardProfit(
          cardType: 'أبو العشرة',
          costPrice: 9800,
          sellingPrice: 10000,
          quantity: 3,
          date: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];

      for (final profit in sampleProfits) {
        try {
          await profitProvider.addProfit(profit);
        } catch (e) {
          debugPrint('خطأ في إضافة البيانات التجريبية: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إدارة أرباح البطاقات',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.green.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showPriceSettingsDialog(context),
            tooltip: 'إعدادات الأسعار',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showProfitAnalytics(context),
            tooltip: 'تحليل الأرباح',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<CardProfitProvider>(
                context,
                listen: false,
              ).loadProfits();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Consumer<CardProfitProvider>(
        builder: (context, profitProvider, child) {
          if (profitProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (profitProvider.profits.isEmpty) {
            return _buildEmptyState();
          }

          return _buildProfitsList(profitProvider);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddProfitDialog(context),
        backgroundColor: Colors.green.shade600,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.trending_up, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد أرباح مسجلة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة ربح جديد',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitsList(CardProfitProvider profitProvider) {
    return Column(
      children: [
        // إجمالي الأرباح
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إجمالي الأرباح',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      NumberFormatter.formatCurrency(
                        profitProvider.getTotalProfits(),
                      ),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // قائمة الأرباح
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: profitProvider.profits.length,
            itemBuilder: (context, index) {
              final profit = profitProvider.profits[index];
              return _buildProfitCard(profit);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfitCard(CardProfit profit) {
    // حساب عدد الأيام منذ الإضافة
    final daysSinceAdded = DateTime.now().difference(profit.createdAt).inDays;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getCardTypeColor(profit.cardType),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    profit.cardType,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                // عداد الأيام
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'منذ $daysSinceAdded يوم',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // زر الحذف
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  onPressed: () => _showDeleteConfirmation(profit),
                  tooltip: 'حذف',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // تاريخ الإضافة
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  'تاريخ الإضافة: ${DateFormat('yyyy/MM/dd').format(profit.date)}',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'سعر الشراء',
                    NumberFormatter.formatCurrency(profit.costPrice),
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'سعر البيع',
                    NumberFormatter.formatCurrency(profit.sellingPrice),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('الكمية', '${profit.quantity} بطاقة'),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'إجمالي الربح',
                    NumberFormatter.formatCurrency(profit.totalProfit),
                    isProfit: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, {bool isProfit = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isProfit ? Colors.green.shade600 : Colors.black87,
          ),
        ),
      ],
    );
  }

  Color _getCardTypeColor(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو العشرة':
        return Colors.teal;
      case 'أبو الستة':
        return Colors.blue;
      case 'نقدي':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // الحصول على قائمة فريدة من أنواع البطاقات
  List<DropdownMenuItem<String>> _getUniqueCardTypes(
    CardTypeProvider cardTypeProvider,
  ) {
    // قائمة أنواع البطاقات - فقط الأنواع المخصصة
    final cardTypes = <String>{};

    // إضافة الأنواع المخصصة فقط
    try {
      for (final cardType in cardTypeProvider.allCardTypes) {
        if (cardType.displayName.isNotEmpty) {
          cardTypes.add(cardType.displayName);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع البطاقات: $e');
    }

    // تحويل إلى قائمة مرتبة
    final sortedTypes = cardTypes.toList()..sort();

    return sortedTypes.map((cardType) {
      return DropdownMenuItem<String>(
        value: cardType,
        child: Text(cardType, style: const TextStyle(fontSize: 16)),
      );
    }).toList();
  }

  void _showAddProfitDialog(BuildContext context) {
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );
    String? selectedCardType;
    final costPriceController = TextEditingController();
    final sellingPriceController = TextEditingController();
    final quantityController = TextEditingController();
    DateTime selectedDate = DateTime.now();

    // تحميل أنواع البطاقات مسبق<|im_start|>
    final availableCardTypes = _getUniqueCardTypes(cardTypeProvider);

    // التأكد من وجود أنواع بطاقات
    if (availableCardTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد أنواع بطاقات متاحة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.trending_up, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'إضافة ربح جديد',
                style: TextStyle(
                  color: Colors.black87, // لون أسود ثابت للعنوان لضمان الوضوح
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اختيار نوع البطاقة
                DropdownButtonFormField<String>(
                  value: selectedCardType,
                  decoration: const InputDecoration(
                    labelText: 'نوع البطاقة',
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  dropdownColor: Colors.white, // خلفية بيضاء للقائمة المنسدلة
                  items: availableCardTypes,
                  onChanged: (value) {
                    setState(() {
                      selectedCardType = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // سعر الشراء
                TextField(
                  controller: costPriceController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'سعر الشراء',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.money_off),
                  ),
                ),
                const SizedBox(height: 16),

                // سعر البيع
                TextField(
                  controller: sellingPriceController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'سعر البيع',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                ),
                const SizedBox(height: 16),

                // الكمية
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.numbers),
                  ),
                ),
                const SizedBox(height: 16),

                // التاريخ
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: Text(
                    'التاريخ: ${DateFormat('yyyy/MM/dd', 'en').format(selectedDate)}',
                    style: const TextStyle(
                      color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                      fontSize: 16,
                    ),
                  ),
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                    );
                    if (picked != null) {
                      setState(() {
                        selectedDate = picked;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedCardType != null &&
                    costPriceController.text.isNotEmpty &&
                    sellingPriceController.text.isNotEmpty &&
                    quantityController.text.isNotEmpty) {
                  try {
                    final costPrice = double.parse(costPriceController.text);
                    final sellingPrice = double.parse(
                      sellingPriceController.text,
                    );
                    final quantity = int.parse(quantityController.text);

                    final profit = CardProfit(
                      cardType: selectedCardType!,
                      costPrice: costPrice,
                      sellingPrice: sellingPrice,
                      quantity: quantity,
                      date: selectedDate,
                    );

                    final profitProvider = Provider.of<CardProfitProvider>(
                      context,
                      listen: false,
                    );
                    await profitProvider.addProfit(profit);

                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إضافة الربح بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPriceSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.settings,
                      color: Colors.green.shade600,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Text(
                      'إعدادات أسعار الشراء',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Description
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade600),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'أسعار الشراء الافتراضية المستخدمة في حساب الأرباح التلقائية',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Price List
              const Text(
                'قائمة الأسعار الحالية:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              ..._buildPriceItems(),

              const SizedBox(height: 20),

              // Note
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'يتم حساب الربح تلقائياً عند إضافة دين جديد بناءً على هذه الأسعار. يمكنك تعديل الأسعار يدوياً عند إضافة كل عملية.',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey.shade600,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('إغلاق'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      // يمكن إضافة وظيفة تعديل الأسعار هنا
                      _showEditPricesDialog(context);
                    },
                    icon: const Icon(Icons.edit, size: 18),
                    label: const Text('تعديل الأسعار'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildPriceItems() {
    final prices = [
      {'name': 'زين', 'price': 6500, 'color': Colors.purple},
      {'name': 'آسيا', 'price': 7500, 'color': Colors.blue},
      {'name': 'أبو العشرة', 'price': 9800, 'color': Colors.green},
      {'name': 'أبو الستة', 'price': 4800, 'color': Colors.orange},
      {'name': 'نقدي', 'price': 0, 'color': Colors.grey},
    ];

    return prices.map((item) {
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: (item['color'] as Color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.credit_card,
                color: item['color'] as Color,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'] as String,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item['price'] == 0 ? 'لا توجد تكلفة' : 'سعر الشراء',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            Text(
              item['price'] == 0
                  ? 'مجاني'
                  : NumberFormatter.formatCurrency(
                      (item['price'] as int).toDouble(),
                    ),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: item['color'] as Color,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  void _showEditPricesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الأسعار'),
        content: const Text('هذه الميزة ستكون متاحة في التحديث القادم'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showProfitAnalytics(BuildContext context) {
    final profitProvider = Provider.of<CardProfitProvider>(
      context,
      listen: false,
    );
    final topCards = profitProvider.getTopProfitableCards();
    final totalProfits = profitProvider.getTotalProfits();
    final totalCards = profitProvider.profits.length;
    final averageProfit = totalCards > 0 ? totalProfits / totalCards : 0.0;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.analytics,
                      color: Colors.blue.shade600,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Text(
                      'تحليل الأرباح المتقدم',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Statistics Cards
              Row(
                children: [
                  Expanded(
                    child: _buildAnalyticsCard(
                      'إجمالي الأرباح',
                      NumberFormatter.formatCurrency(totalProfits),
                      Icons.attach_money,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildAnalyticsCard(
                      'عدد البطاقات',
                      totalCards.toString(),
                      Icons.inventory_2,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildAnalyticsCard(
                'متوسط الربح',
                NumberFormatter.formatCurrency(averageProfit),
                Icons.trending_up,
                Colors.orange,
              ),
              const SizedBox(height: 24),

              // Top Cards Section
              const Text(
                'أفضل البطاقات ربحاً',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: topCards.take(5).map((card) {
                    final index = topCards.indexOf(card);
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: _getCardTypeColor(card['cardType']),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  card['cardType'],
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    card['totalProfit'],
                                  ),
                                  style: TextStyle(
                                    color: Colors.green.shade600,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${card['count']} بطاقة',
                              style: TextStyle(
                                color: Colors.green.shade700,
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 24),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey.shade600,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('إغلاق'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      // يمكن إضافة وظيفة تصدير التقرير هنا
                    },
                    icon: const Icon(Icons.file_download, size: 18),
                    label: const Text('تصدير التقرير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // دالة إظهار تأكيد الحذف
  void _showDeleteConfirmation(CardProfit profit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'تأكيد الحذف',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          content: Text(
            'هل أنت متأكد من حذف بطاقة "${profit.cardType}"؟\n\nسيتم حذف جميع بيانات الربح المرتبطة بها.',
            style: const TextStyle(color: Colors.black87),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteProfit(profit);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  // دالة حذف الربح
  Future<void> _deleteProfit(CardProfit profit) async {
    try {
      final profitProvider = Provider.of<CardProfitProvider>(
        context,
        listen: false,
      );
      await profitProvider.deleteProfit(profit.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف بطاقة "${profit.cardType}" بنجاح'),
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف البطاقة: $e'),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
