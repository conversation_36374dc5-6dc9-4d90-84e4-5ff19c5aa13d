import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'providers/customer_provider.dart';
import 'providers/debt_provider.dart';

import 'providers/card_type_provider.dart';
import 'providers/card_profit_provider.dart';
import 'providers/card_inventory_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/smart_notification_provider.dart';
import 'providers/form_data_provider.dart';
import 'providers/font_provider.dart';

import 'screens/splash_screen.dart';

void main() async {
  // Force rebuild - Version 4 with UI improvements
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Arabic locale for date formatting
  await initializeDateFormatting('ar');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => DebtProvider()),

        ChangeNotifierProvider(
          create: (_) {
            final provider = CardTypeProvider();
            // تحميل البيانات فوراً عند إنشاء المزود
            WidgetsBinding.instance.addPostFrameCallback((_) {
              provider.loadCustomCardTypes();
            });
            return provider;
          },
        ), // Added
        ChangeNotifierProvider(create: (_) => CardProfitProvider()), // Added
        ChangeNotifierProvider(
          create: (_) => CardInventoryProvider(),
        ), // Added - NEW SYSTEM!
        ChangeNotifierProvider(create: (_) => NotificationProvider()), // Added
        ChangeNotifierProvider(
          create: (_) => SmartNotificationProvider(),
        ), // Added - Smart Notifications
        ChangeNotifierProvider(create: (_) => FormDataProvider()), // Added
        ChangeNotifierProvider(
          create: (_) => FontProvider(),
        ), // Added - Font Settings
      ],
      child: Consumer<FontProvider>(
        builder: (context, fontProvider, child) {
          return MaterialApp(
            title: 'محاسب ديون احترافي',
            debugShowCheckedModeBanner: false,
            locale: const Locale('ar', 'SA'),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('ar', 'SA'), Locale('en', 'US')],
            theme: fontProvider.getTheme(
              ThemeData(
                primarySwatch: Colors.teal,
                primaryColor: const Color(0xFF00695C),
                colorScheme: ColorScheme.fromSeed(
                  seedColor: const Color(0xFF00695C),
                  primary: const Color(0xFF00695C),
                  secondary: const Color(0xFF4CAF50),
                  tertiary: const Color(0xFF2196F3),
                  surface: const Color(0xFFF8F9FA),
                ),
                fontFamily: fontProvider.settings.fontFamily,
                appBarTheme: const AppBarTheme(
                  backgroundColor: Color(0xFF00695C),

                  foregroundColor: Colors.white,
                  elevation: 0,
                  centerTitle: true,
                  titleTextStyle: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  iconTheme: IconThemeData(color: Colors.white),
                ),
                cardTheme: CardThemeData(
                  elevation: 6,
                  shadowColor: Colors.grey.withValues(alpha: 0.3),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  color: Colors.white,
                ),
                elevatedButtonTheme: ElevatedButtonThemeData(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF00695C),
                    foregroundColor: Colors.white,
                    elevation: 4,
                    shadowColor: Colors.grey.withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
                floatingActionButtonTheme: const FloatingActionButtonThemeData(
                  backgroundColor: Color(0xFF4CAF50),
                  foregroundColor: Colors.white,
                  elevation: 8,
                ),
                inputDecorationTheme: InputDecorationTheme(
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF00695C),
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                useMaterial3: true,
              ),
            ),
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}
