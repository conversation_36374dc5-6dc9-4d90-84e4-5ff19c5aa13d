import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/customer_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/smart_notification_provider.dart';
import '../providers/form_data_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/font_provider.dart';

import '../models/customer.dart';
import '../models/font_settings.dart';
import '../database/database_helper.dart';
import '../utils/database_test.dart';

import '../widgets/customer_card.dart';
import '../widgets/search_bar_widget.dart';

import '../widgets/add_debt_bottom_sheet.dart';
import '../services/smart_monitoring_service.dart';
import 'card_profit_screen.dart';
import 'card_inventory_screen.dart';
import 'customer_detail_screen.dart';
import 'reports_screen.dart';
import 'statistics_screen.dart';
import 'smart_notifications_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isSelectionMode = false;
  Set<int> _selectedCustomerIds = {};

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedCustomerIds.clear();
      }
    });
  }

  void _selectAllCustomers(List<Customer> customers) {
    setState(() {
      _selectedCustomerIds = customers.map((c) => c.id!).toSet();
    });
  }

  void _deleteSelectedCustomers() async {
    if (_selectedCustomerIds.isEmpty) return;

    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'تأكيد الحذف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل تريد حذف ${_selectedCustomerIds.length} عميل؟',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      for (final customerId in _selectedCustomerIds) {
        await customerProvider.deleteCustomer(customerId);
      }
      _toggleSelectionMode();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف ${_selectedCustomerIds.length} عميل'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
      Provider.of<NotificationProvider>(context, listen: false).loadSettings();
      Provider.of<FormDataProvider>(context, listen: false).loadFormData();
      Provider.of<CardTypeProvider>(
        context,
        listen: false,
      ).loadCustomCardTypes();
      Provider.of<FontProvider>(context, listen: false).loadSettings();

      // لا تحميل تلقائي للمخزون - المستخدم يديره بنفسه

      // تهيئة وبدء المراقبة الذكية
      _initializeSmartMonitoring();
    });
  }

  void _initializeSmartMonitoring() {
    final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
      context,
      listen: false,
    );
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );

    // تهيئة خدمة المراقبة الذكية
    SmartMonitoringService().initialize(
      notificationProvider: smartNotificationProvider,
      debtProvider: debtProvider,
      customerProvider: customerProvider,
      inventoryProvider: inventoryProvider,
    );

    // بدء المراقبة التلقائية
    SmartMonitoringService().startMonitoring();

    // تحديث فوري للتنبيهات
    _refreshNotifications();
  }

  // تحديث التنبيهات فوراً
  Future<void> _refreshNotifications() async {
    final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
      context,
      listen: false,
    );
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );

    // تحديث البيانات أولاً
    await debtProvider.loadAllDebts();
    await customerProvider.loadCustomers();

    // ثم إنشاء التنبيهات
    await smartNotificationProvider.generateSmartNotifications(
      debtProvider: debtProvider,
      customerProvider: customerProvider,
      inventoryProvider: inventoryProvider,
    );

    debugPrint(
      '🔄 تم تحديث التنبيهات - العدد: ${smartNotificationProvider.notifications.length}',
    );
  }

  @override
  void dispose() {
    // إيقاف المراقبة الذكية عند إغلاق الشاشة
    SmartMonitoringService().stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade600,
                Colors.blue.shade800,
                Colors.indigo.shade700,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
        ),
        toolbarHeight: 80,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.3),
                    Colors.white.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'محاسب الديون',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                  Text(
                    'إدارة متكاملة للمبيعات والأرباح',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          Builder(
            builder: (context) => IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.menu, color: Colors.white, size: 24),
              ),
              onPressed: () => Scaffold.of(context).openEndDrawer(),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      endDrawer: _buildDrawer(),
      body: Column(
        children: [
          // Selection Mode Toolbar
          if (_isSelectionMode)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border(
                  bottom: BorderSide(color: Colors.blue.shade200, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: _toggleSelectionMode,
                    icon: const Icon(Icons.close, size: 20),
                    tooltip: 'إلغاء التحديد',
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تم تحديد ${_selectedCustomerIds.length} عميل',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Consumer<CustomerProvider>(
                    builder: (context, customerProvider, _) {
                      return TextButton(
                        onPressed: () =>
                            _selectAllCustomers(customerProvider.customers),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          minimumSize: const Size(0, 32),
                        ),
                        child: Text(
                          _selectedCustomerIds.length ==
                                  customerProvider.customers.length
                              ? 'إلغاء تحديد الكل'
                              : 'تحديد الكل',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 4),
                  ElevatedButton.icon(
                    onPressed: _selectedCustomerIds.isNotEmpty
                        ? _deleteSelectedCustomers
                        : null,
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('حذف', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      minimumSize: const Size(0, 32),
                    ),
                  ),
                ],
              ),
            ),

          // Search Bar
          if (!_isSelectionMode)
            Container(
              padding: const EdgeInsets.all(16),
              child: const SearchBarWidget(),
            ),

          // Customers List
          Expanded(
            child: Consumer<CustomerProvider>(
              builder: (context, customerProvider, child) {
                if (customerProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (customerProvider.customers.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 80,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا يوجد عملاء',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'اضغط على زر + لإضافة دين جديد',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: customerProvider.customers.length,
                  itemBuilder: (context, index) {
                    final customer = customerProvider.customers[index];
                    return CustomerCard(
                      customer: customer,
                      index: index,
                      isSelectionMode: _isSelectionMode,
                      isSelected: _selectedCustomerIds.contains(customer.id),
                      onTap: () {
                        if (!_isSelectionMode) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  CustomerDetailScreen(customer: customer),
                            ),
                          );
                        }
                      },
                      onLongPress: () {
                        if (!_isSelectionMode) {
                          _toggleSelectionMode();
                          setState(() {
                            _selectedCustomerIds.add(customer.id!);
                          });
                        }
                      },
                      onSelectionChanged: (isSelected) {
                        setState(() {
                          if (isSelected) {
                            _selectedCustomerIds.add(customer.id!);
                          } else {
                            _selectedCustomerIds.remove(customer.id!);
                          }
                        });
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),

      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddDebtBottomSheet(context, null);
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,

      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        selectedFontSize: 12,
        unselectedFontSize: 10,
        currentIndex: 1, // Home is active
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: 'الإحصائيات',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(
            icon: Icon(Icons.assessment),
            label: 'التقارير',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
        onTap: (index) {
          switch (index) {
            case 0:
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatisticsScreen(),
                ),
              );
              break;
            case 1:
              // Already on home
              break;
            case 2:
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ReportsScreen()),
              );
              break;
            case 3:
              _showSettingsBottomSheet(context);
              break;
          }
        },
      ),
    );
  }

  Widget _buildDrawer() {
    return SizedBox(
      width: 320,
      child: Drawer(
        backgroundColor: Colors.white,
        child: Column(
          children: [
            // Header
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.blue.shade600, Colors.blue.shade800],
                ),
              ),
              child: SafeArea(
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ),
            ),

            // Menu Items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 8),
                children: [
                  _buildDrawerItem(
                    icon: Icons.refresh_rounded,
                    title: 'تحديث التنبيهات',
                    color: Colors.blue.shade600,
                    onTap: () async {
                      Navigator.pop(context);
                      await _refreshNotifications();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث التنبيهات'),
                            backgroundColor: Colors.blue,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.notifications_rounded,
                    title: 'التنبيهات الذكية',
                    color: Colors.orange.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const SmartNotificationsScreen(),
                        ),
                      );
                    },
                    trailing: Consumer<SmartNotificationProvider>(
                      builder: (context, notificationProvider, _) {
                        if (notificationProvider.unreadCount > 0) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${notificationProvider.unreadCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  const Divider(height: 1),
                  _buildDrawerItem(
                    icon: Icons.inventory_2_rounded,
                    title: 'إدارة الكميات',
                    color: Colors.blue.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CardInventoryScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.trending_up_rounded,
                    title: 'تتبع الأرباح',
                    color: Colors.green.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CardProfitScreen(),
                        ),
                      );
                    },
                  ),

                  const Divider(height: 1),
                  _buildDrawerItem(
                    icon: Icons.cloud_upload_rounded,
                    title: 'النسخ الاحتياطي',
                    color: Colors.teal.shade600,
                    onTap: () {
                      Navigator.pop(context);
                      _showBackupDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 28),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        trailing:
            trailing ??
            const Icon(Icons.chevron_left, color: Colors.grey, size: 24),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showAddDebtBottomSheet(BuildContext context, Customer? customer) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddDebtBottomSheet(customer: customer),
    );
  }

  void _showSettingsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
          ),
          child: Column(
            children: [
              // Drag Handle
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 32,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
                child: Row(
                  children: [
                    Icon(
                      Icons.settings_rounded,
                      size: 28,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'الإعدادات',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close_rounded),
                      style: IconButton.styleFrom(
                        backgroundColor: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        foregroundColor: Theme.of(
                          context,
                        ).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: ListView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  children: [
                    // Notifications Section
                    _buildSectionHeader('التنبيهات والإشعارات'),
                    _buildModernSettingTile(
                      icon: Icons.notifications_active_rounded,
                      title: 'إعدادات التنبيهات',
                      subtitle: 'تنبيهات المخزن والكميات المتبقية',
                      onTap: () {
                        Navigator.pop(context);
                        _showNotificationSettings(context);
                      },
                    ),

                    const SizedBox(height: 24),

                    // Appearance Section
                    _buildSectionHeader('المظهر والعرض'),
                    _buildModernSettingTile(
                      icon: Icons.text_fields_rounded,
                      title: 'إعدادات الخط',
                      subtitle: 'تكبير الخط وتغيير نوع الخط',
                      onTap: () {
                        Navigator.pop(context);
                        _showFontSettings(context);
                      },
                    ),

                    const SizedBox(height: 24),

                    // Database Section
                    _buildSectionHeader('قاعدة البيانات'),
                    _buildModernSettingTile(
                      icon: Icons.cleaning_services_rounded,
                      title: 'تنظيف قاعدة البيانات',
                      subtitle: 'إزالة البيانات المعلقة وتحسين الأداء',
                      onTap: () {
                        Navigator.pop(context);
                        _showCleanupDialog(context);
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildModernSettingTile(
                      icon: Icons.analytics_rounded,
                      title: 'إحصائيات قاعدة البيانات',
                      subtitle: 'عرض معلومات وإحصائيات قاعدة البيانات',
                      onTap: () {
                        Navigator.pop(context);
                        _showDatabaseStatsDialog(context);
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildModernSettingTile(
                      icon: Icons.bug_report_rounded,
                      title: 'اختبار قاعدة البيانات',
                      subtitle: 'فحص شامل لسلامة قاعدة البيانات',
                      onTap: () {
                        Navigator.pop(context);
                        _showDatabaseTestDialog(context);
                      },
                    ),

                    const SizedBox(height: 24),

                    // Backup Section
                    _buildSectionHeader('النسخ الاحتياطي'),
                    _buildModernSettingTile(
                      icon: Icons.backup_rounded,
                      title: 'نسخ احتياطي',
                      subtitle: 'إنشاء نسخة احتياطية من البيانات',
                      onTap: () {
                        Navigator.pop(context);
                        _showBackupDialog(context);
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildModernSettingTile(
                      icon: Icons.restore_rounded,
                      title: 'استعادة البيانات',
                      subtitle: 'استعادة البيانات من نسخة احتياطية',
                      onTap: () {
                        Navigator.pop(context);
                        _showRestoreDialog(context);
                      },
                    ),

                    const SizedBox(height: 24),

                    // About Section
                    _buildSectionHeader('معلومات التطبيق'),
                    _buildModernSettingTile(
                      icon: Icons.info_rounded,
                      title: 'حول التطبيق',
                      subtitle: 'معلومات التطبيق والإصدار',
                      onTap: () {
                        Navigator.pop(context);
                        _showAboutDialog(context);
                      },
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء عنوان القسم
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 12),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  // بناء عنصر إعدادات حديث
  Widget _buildModernSettingTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      color: Theme.of(context).colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outlineVariant,
          width: 0.5,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 28,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right_rounded,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange, Colors.orange.shade600],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.notifications_active,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'إعدادات التنبيهات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        content: Consumer<NotificationProvider>(
          builder: (context, notificationProvider, child) {
            // Force rebuild when settings change
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Sales Notifications
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.shopping_cart,
                              color: Colors.blue.shade600,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: Text(
                                'تنبيهات المبيعات',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Switch(
                              key: const ValueKey('sales_notifications_switch'),
                              value: notificationProvider
                                  .settings
                                  .salesNotificationsEnabled,
                              onChanged: (value) async {
                                await notificationProvider
                                    .updateSalesNotifications(value);
                              },
                              activeColor: Colors.blue,
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          'سيتم تنبيهك عند كل عملية بيع بالتفاصيل التالية:',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '• نوع الكارت المباع',
                          style: TextStyle(fontSize: 13),
                        ),
                        const Text(
                          '• الكمية المباعة',
                          style: TextStyle(fontSize: 13),
                        ),
                        const Text(
                          '• المبلغ الإجمالي',
                          style: TextStyle(fontSize: 13),
                        ),
                        const Text(
                          '• اسم العميل',
                          style: TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Notification Duration Settings
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.purple.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.timer,
                              color: Colors.purple.shade600,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'مدة عرض التنبيهات',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: Text(
                            'المدة الحالية: ${notificationProvider.settings.notificationDuration} ثانية',
                            key: ValueKey(
                              notificationProvider
                                  .settings
                                  .notificationDuration,
                            ),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Slider(
                          key: const ValueKey('notification_duration_slider'),
                          value: notificationProvider
                              .settings
                              .notificationDuration
                              .toDouble(),
                          min: 1,
                          max: 10,
                          divisions: 9,
                          label:
                              '${notificationProvider.settings.notificationDuration} ثانية',
                          onChanged: (value) async {
                            await notificationProvider
                                .updateNotificationDuration(value.round());
                          },
                          activeColor: Colors.purple,
                        ),
                        const Text(
                          'اختر المدة المناسبة لعرض التنبيهات (1-10 ثواني)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Inventory Notifications
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.notifications,
                              color: Colors.blue.shade600,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: Text(
                                'تنبيهات عامة',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Switch(
                              key: const ValueKey(
                                'general_notifications_switch',
                              ),
                              value: notificationProvider
                                  .settings
                                  .inventoryNotificationsEnabled,
                              onChanged: (value) async {
                                await notificationProvider
                                    .updateInventoryNotifications(value);
                              },
                              activeColor: Colors.blue,
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: Text(
                            'حد التنبيه: ${notificationProvider.settings.lowStockThreshold} كروت أو أقل',
                            key: ValueKey(
                              notificationProvider.settings.lowStockThreshold,
                            ),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Slider(
                          key: const ValueKey('low_stock_threshold_slider'),
                          value: notificationProvider.settings.lowStockThreshold
                              .toDouble(),
                          min: 1,
                          max: 50,
                          divisions: 49,
                          label:
                              '${notificationProvider.settings.lowStockThreshold} كروت',
                          onChanged: (value) async {
                            await notificationProvider.updateLowStockThreshold(
                              value.round(),
                            );
                          },
                          activeColor: Colors.red,
                        ),
                        const Text(
                          'سيتم تنبيهك عند:',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          '• انخفاض الكمية للحد المحدد',
                          style: TextStyle(fontSize: 13),
                        ),
                        const Text(
                          '• نفاد الكمية تماماً',
                          style: TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              return ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حفظ إعدادات التنبيهات بنجاح'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('حفظ'),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showCleanupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.cleaning_services,
                color: Colors.orange.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('تنظيف قاعدة البيانات'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم تنظيف قاعدة البيانات من:'),
            SizedBox(height: 12),
            Text('• المدفوعات المعلقة'),
            Text('• الديون بدون عملاء'),
            Text('• تحسين مساحة التخزين'),
            SizedBox(height: 12),
            Text(
              'هل تريد المتابعة؟',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performCleanup(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('تنظيف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _performCleanup(BuildContext context) async {
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري التنظيف...'),
          ],
        ),
      ),
    );

    try {
      final dbHelper = DatabaseHelper();
      await dbHelper.cleanupDatabase();

      if (mounted) {
        navigator.pop();
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('تم تنظيف قاعدة البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        navigator.pop();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('خطأ في التنظيف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDatabaseStatsDialog(BuildContext context) async {
    final navigator = Navigator.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تحميل الإحصائيات...'),
          ],
        ),
      ),
    );

    try {
      final dbHelper = DatabaseHelper();
      final stats = await dbHelper.getDatabaseInfo();

      if (mounted && context.mounted) {
        navigator.pop();
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.analytics,
                    color: Colors.blue.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text('إحصائيات قاعدة البيانات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow('إصدار قاعدة البيانات', '${stats['version']}'),
                _buildStatRow('عدد العملاء', '${stats['customers']}'),
                _buildStatRow('عدد الديون', '${stats['debts']}'),
                _buildStatRow('عدد المدفوعات', '${stats['payments']}'),
                _buildStatRow('عدد عناصر المخزن', '${stats['inventory']}'),
                _buildStatRow(
                  'أنواع الكروت المخصصة',
                  '${stats['customCardTypes']}',
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(
            value,
            style: TextStyle(
              color: Colors.blue.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.info, color: Colors.blue.shade600, size: 24),
            ),
            const SizedBox(width: 12),
            const Text('حول التطبيق'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تطبيق محاسب الديون',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('الإصدار: 2.0.0'),
            SizedBox(height: 8),
            Text('تطبيق شامل لإدارة الديون والعملاء والمخزن'),
            SizedBox(height: 12),
            Text('الميزات:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('• إدارة العملاء والديون'),
            Text('• نظام المدفوعات'),
            Text('• إدارة المخزن'),
            Text('• إحصائيات متقدمة'),
            Text('• نسخ احتياطي'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.backup, color: Colors.green.shade600, size: 24),
            ),
            const SizedBox(width: 12),
            const Text('نسخ احتياطي'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ميزة النسخ الاحتياطي قيد التطوير'),
            SizedBox(height: 12),
            Text('ستتمكن قريباً من:'),
            Text('• إنشاء نسخة احتياطية من البيانات'),
            Text('• حفظها في التخزين المحلي'),
            Text('• مشاركتها عبر التطبيقات'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.restore, color: Colors.blue.shade600, size: 24),
            ),
            const SizedBox(width: 12),
            const Text('استعادة البيانات'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ميزة استعادة البيانات قيد التطوير'),
            SizedBox(height: 12),
            Text('ستتمكن قريباً من:'),
            Text('• اختيار ملف النسخة الاحتياطية'),
            Text('• استعادة البيانات بأمان'),
            Text('• دمج البيانات الجديدة مع الموجودة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // Professional customer deletion with multiple confirmations
  Future<void> _showDeleteCustomerDialog(
    BuildContext context,
    Customer customer,
  ) async {
    try {
      // First, check if customer has any debts
      final dbHelper = DatabaseHelper();

      // Get customer data directly
      final customerDebts = await dbHelper.getCustomerDebts(customer.id!);
      final customerPayments = await dbHelper.getCustomerPayments(customer.id!);

      debugPrint(
        'Checking customer ${customer.name} (ID: ${customer.id}) - Debts: ${customerDebts.length}, Payments: ${customerPayments.length}',
      );

      // Clean up orphaned payments (payments without corresponding debts)
      var finalPayments = customerPayments;
      if (customerPayments.isNotEmpty && customerDebts.isEmpty) {
        debugPrint('Found orphaned payments, cleaning up...');
        await dbHelper.cleanupDatabase();

        // Re-check after cleanup
        finalPayments = await dbHelper.getCustomerPayments(customer.id!);
        debugPrint('Payments after cleanup: ${finalPayments.length}');
      }

      if (!mounted) return;

      if (!mounted || !context.mounted) return;

      if (customerDebts.isNotEmpty || finalPayments.isNotEmpty) {
        _showCannotDeleteDialog(
          context,
          customer,
          customerDebts.length,
          finalPayments.length,
        );
        return;
      }

      // If no debts or payments, show confirmation dialog
      _showConfirmDeleteDialog(context, customer);
    } catch (e) {
      debugPrint('Error checking customer data: $e');
      if (!mounted || !context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التحقق من بيانات العميل: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showCannotDeleteDialog(
    BuildContext context,
    Customer customer,
    int debtsCount,
    int paymentsCount,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.warning_amber,
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'لا يمكن حذف العميل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لا يمكن حذف العميل "${customer.name}" لأنه يحتوي على:',
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
            const SizedBox(height: 16),
            if (debtsCount > 0)
              _buildWarningItem(
                icon: Icons.receipt_long,
                text: '$debtsCount دين مسجل',
                color: Colors.orange,
              ),
            if (paymentsCount > 0)
              _buildWarningItem(
                icon: Icons.payment,
                text: '$paymentsCount عملية دفع',
                color: Colors.blue,
              ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.amber.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'يجب حذف جميع الديون والمدفوعات أولاً',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('فهمت', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      CustomerDetailScreen(customer: customer),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إدارة العميل',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _showConfirmDeleteDialog(BuildContext context, Customer customer) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.delete_forever,
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.red.shade50, Colors.red.shade100],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.red.shade600,
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'هل أنت متأكد من حذف العميل؟',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '"${customer.name}"',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '⚠️ هذا الإجراء لا يمكن التراجع عنه',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.red,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () => _performDeleteCustomer(context, customer),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف نهائي',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningItem({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteCustomer(
    BuildContext context,
    Customer customer,
  ) async {
    Navigator.pop(context); // Close confirmation dialog

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Delete customer
      await Provider.of<CustomerProvider>(
        context,
        listen: false,
      ).deleteCustomer(customer.id!);

      if (!mounted || !context.mounted) return;

      Navigator.pop(context); // Close loading dialog

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('تم حذف العميل "${customer.name}" بنجاح'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      if (!mounted || !context.mounted) return;

      Navigator.pop(context); // Close loading dialog

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('فشل في حذف العميل: ${e.toString()}'),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Font Settings Dialog
  void _showFontSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        elevation: 16,
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          height: MediaQuery.of(context).size.height * 0.85,
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.blue.shade50],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(28),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade600, Colors.blue.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.text_fields,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إعدادات الخط',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            'تخصيص حجم ونوع الخط حسب احتياجاتك',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(28),
                  child: Consumer<FontProvider>(
                    builder: (context, fontProvider, _) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFontSizeSection(fontProvider),
                          const SizedBox(height: 32),
                          _buildFontFamilySection(fontProvider),
                          const SizedBox(height: 32),
                          _buildFontWeightSection(fontProvider),
                          const SizedBox(height: 32),
                          _buildPreviewSection(fontProvider),
                          const SizedBox(height: 20), // مساحة إضافية في النهاية
                        ],
                      );
                    },
                  ),
                ),
              ),

              // Footer
              Container(
                padding: const EdgeInsets.all(28),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () async {
                          await Provider.of<FontProvider>(
                            context,
                            listen: false,
                          ).resetToDefault();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                          side: BorderSide(
                            color: Colors.orange.shade400,
                            width: 2,
                          ),
                        ),
                        child: Text(
                          'إعادة تعيين',
                          style: TextStyle(
                            color: Colors.orange.shade600,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                          elevation: 3,
                        ),
                        child: const Text(
                          'تطبيق',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Font Size Section
  Widget _buildFontSizeSection(FontProvider fontProvider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.format_size, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 12),
              const Text(
                'حجم الخط',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'الحجم الحالي: ${fontProvider.settings.fontSizeName}',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Slider(
            value: fontProvider.settings.fontSize,
            min: 0.8,
            max: 2.0,
            divisions: 12,
            label: fontProvider.settings.fontSizeName,
            onChanged: (value) async {
              await fontProvider.updateFontSize(value);
            },
            activeColor: Colors.blue.shade600,
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: FontSettings.fontSizes.entries.map((entry) {
                final isSelected =
                    entry.value == fontProvider.settings.fontSize;
                return InkWell(
                  onTap: () async {
                    await fontProvider.updateFontSize(entry.value);
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue.shade600 : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue.shade600
                            : Colors.grey.shade300,
                        width: 2,
                      ),
                    ),
                    child: Text(
                      entry.key,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? Colors.white : Colors.grey.shade700,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // Font Family Section
  Widget _buildFontFamilySection(FontProvider fontProvider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.font_download, color: Colors.green.shade600, size: 24),
              const SizedBox(width: 12),
              const Text(
                'نوع الخط',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'الخط الحالي: ${fontProvider.settings.fontFamily}',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: FontSettings.availableFonts.map((font) {
              final isSelected = font == fontProvider.settings.fontFamily;
              return InkWell(
                onTap: () async {
                  await fontProvider.updateFontFamily(font);
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.green.shade600 : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? Colors.green.shade600
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Text(
                    font,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: font,
                      color: isSelected ? Colors.white : Colors.grey.shade700,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Font Weight Section
  Widget _buildFontWeightSection(FontProvider fontProvider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.format_bold, color: Colors.orange.shade600, size: 24),
              const SizedBox(width: 12),
              const Text(
                'وزن الخط',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'الوزن الحالي: ${fontProvider.settings.fontWeightName}',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: FontSettings.fontWeights.entries.map((entry) {
              final isSelected =
                  entry.value == fontProvider.settings.fontWeight;
              return Expanded(
                child: InkWell(
                  onTap: () async {
                    await fontProvider.updateFontWeight(entry.value);
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.orange.shade600 : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Colors.orange.shade600
                            : Colors.grey.shade300,
                      ),
                    ),
                    child: Text(
                      entry.key,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: entry.value == 'bold'
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected ? Colors.white : Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Preview Section
  Widget _buildPreviewSection(FontProvider fontProvider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.preview, color: Colors.purple.shade600, size: 24),
              const SizedBox(width: 12),
              const Text(
                'معاينة الخط',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نص تجريبي للمعاينة',
                  style: fontProvider.getTextStyle(
                    baseFontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'هذا نص تجريبي لمعاينة الخط المحدد. يمكنك رؤية كيف سيظهر النص في التطبيق مع الحجم والنوع المختار.',
                  style: fontProvider.getTextStyle(
                    baseFontSize: 18,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'الأرقام الإنجليزية: **********',
                  style: fontProvider.getTextStyle(
                    baseFontSize: 16,
                    color: Colors.blue.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'نص عادي • نص عريض • نص مائل',
                  style: fontProvider.getTextStyle(
                    baseFontSize: 16,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Database Test Dialog
  void _showDatabaseTestDialog(BuildContext context) async {
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري اختبار قاعدة البيانات...'),
          ],
        ),
      ),
    );

    try {
      final results = await DatabaseTest.runDatabaseTests();

      if (mounted && context.mounted) {
        navigator.pop(); // إغلاق مؤشر التحميل

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.green.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text('نتائج اختبار قاعدة البيانات'),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTestResult('الاتصال', results['connection']),
                  _buildTestResult('الجداول', results['tables']),
                  _buildTestResult('العمليات الأساسية', results['operations']),
                  _buildTestResult('الإحصائيات', results['statistics']),
                  _buildTestResult('التنظيف', results['cleanup']),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        Navigator.pop(context);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار قاعدة البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildTestResult(String title, Map<String, dynamic>? result) {
    if (result == null) return const SizedBox.shrink();

    final isSuccess = result['success'] == true;
    final message = result['message'] ?? 'لا توجد رسالة';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSuccess
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  message,
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
