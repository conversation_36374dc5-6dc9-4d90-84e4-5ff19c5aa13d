import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';

import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../screens/add_debt_screen.dart';
import '../utils/number_formatter.dart';
import '../widgets/custom_date_picker.dart';

class DebtCard extends StatelessWidget {
  const DebtCard({super.key, required this.debt, required this.customer});
  final Debt debt;
  final Customer customer;

  @override
  Widget build(BuildContext context) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        // Get the latest debt data from provider for financial info only
        final currentDebt = debtProvider.debts.firstWhere(
          (d) => d.id == debt.id,
          orElse: () => debt, // Fallback to original debt if not found
        );

        // ALWAYS use original debt for dates - never use currentDebt for dates
        final isOverdue =
            DateTime.now().isAfter(debt.dueDate) &&
            currentDebt.status != DebtStatus.paid;
        final dayFormat = DateFormat('EEEE', 'ar');

        // تحديد لون البطاقة حسب حالة الاستحقاق وحالة البيع
        final saleStatusColor = _getSaleStatusColor(debt);

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.grey.shade300, // لون ثابت للحافة
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            children: [
              // Customer Name Bar at the top
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isOverdue
                        ? [
                            const Color(0xFF8B0000), // أحمر دم
                            const Color(0xFF660000), // أحمر دم أغمق
                          ]
                        : [
                            saleStatusColor,
                            saleStatusColor.withValues(alpha: 0.8),
                          ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isOverdue ? Icons.warning : Icons.person,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        customer.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              // Main content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Time Counters at the top
                    Row(
                      children: [
                        // عداد منذ القيد
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.blue.shade200,
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: _buildTimeCounter(
                              'منذ القيد',
                              _getDaysSinceEntry(debt),
                              Icons.event_available,
                              Colors.blue,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16), // مسافة بين العدادين
                        // عداد متبقي للموعد
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.shade200,
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: _buildTimeCounter(
                              'متبقي للموعد',
                              _getDaysUntilDue(debt),
                              Icons.alarm,
                              Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Header
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.inventory_2,
                                size: 18,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'الكمية: ${NumberFormatter.formatNumber(debt.quantity)}', // استخدام البيانات الأصلية
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              currentDebt.status,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _getStatusColor(
                                currentDebt.status,
                              ).withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            currentDebt.status.displayName,
                            style: TextStyle(
                              color: _getStatusColor(currentDebt.status),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Amount Info
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ الإجمالي:',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(
                                  debt.amount,
                                ), // استخدام البيانات الأصلية
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                          if (currentDebt.paidAmount > 0) ...[
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'المبلغ المدفوع:',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    currentDebt.paidAmount,
                                  ),
                                  style: const TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                          if (currentDebt.status == DebtStatus.partiallyPaid &&
                              currentDebt.remainingAmount > 0) ...[
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'المبلغ المتبقي:',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    currentDebt.remainingAmount,
                                  ),
                                  style: TextStyle(
                                    color: isOverdue
                                        ? Colors.red
                                        : Colors.orange,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Card Type and Dates
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getCardTypeColor(debt.cardType).withValues(
                              alpha: 0.1,
                            ), // استخدام البيانات الأصلية
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: _getCardTypeColor(debt.cardType)
                                  .withValues(
                                    alpha: 0.3,
                                  ), // استخدام البيانات الأصلية
                            ),
                          ),
                          child: Consumer<CardTypeProvider>(
                            // Added Consumer
                            builder: (context, cardTypeProvider, _) {
                              final cardTypeOption = cardTypeProvider
                                  .getCardTypeById(
                                    debt.cardType,
                                  ); // استخدام البيانات الأصلية
                              final displayName =
                                  cardTypeOption?.displayName ??
                                  'نوع غير معروف'; // Better fallback
                              return Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _getCardTypeIcon(
                                      debt.cardType,
                                    ), // استخدام البيانات الأصلية
                                    size: 16,
                                    color: _getCardTypeColor(
                                      debt.cardType,
                                    ), // استخدام البيانات الأصلية
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    displayName,
                                    style: TextStyle(
                                      color: _getCardTypeColor(
                                        debt.cardType,
                                      ), // استخدام البيانات الأصلية
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        const Text(
                                          'تاريخ القيد:',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: Colors.black54,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Text(
                                          '${dayFormat.format(debt.entryDate)} - ${_formatDateArabic(debt.entryDate)}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.black87,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        Text(
                                          _formatTime(
                                            debt.entryDate,
                                          ), // التاريخ الأصلي
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.black54,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: const Icon(
                                      Icons.event_note,
                                      size: 16,
                                      color: Colors.blue,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          'تاريخ الاستحقاق:',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: isOverdue
                                                ? Colors.red[400]
                                                : Colors.black54,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Text(
                                          '${dayFormat.format(debt.dueDate)} - ${_formatDateArabic(debt.dueDate)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: isOverdue
                                                ? Colors.red
                                                : Colors.black87,
                                            fontWeight: isOverdue
                                                ? FontWeight.bold
                                                : FontWeight.w600,
                                          ),
                                        ),
                                        Text(
                                          _formatTime(
                                            debt.dueDate,
                                          ), // التاريخ الأصلي
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: isOverdue
                                                ? Colors.red[300]
                                                : Colors.black54,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color:
                                          (isOverdue
                                                  ? Colors.red
                                                  : Colors.orange)
                                              .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Icon(
                                      Icons.schedule,
                                      size: 16,
                                      color: isOverdue
                                          ? Colors.red
                                          : Colors.orange,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'ملاحظات: ${debt.notes}', // استخدام البيانات الأصلية
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // Action Buttons - Only show for unpaid debts
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => _editDebt(context, currentDebt),
                          icon: const Icon(Icons.edit),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          ),
                        ),
                        IconButton(
                          onPressed: () => _deleteDebt(context, currentDebt),
                          icon: const Icon(Icons.delete),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.red.withValues(alpha: 0.1),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () =>
                                _showPaymentDialog(context, false, currentDebt),
                            icon: const Icon(Icons.payment, size: 18),
                            label: const Text('تسديد'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getStatusColor(DebtStatus status) {
    switch (status) {
      case DebtStatus.pending:
        return Colors.orange;
      case DebtStatus.partiallyPaid:
        return Colors.blue;
      case DebtStatus.paid:
        return Colors.green;
    }
  }

  IconData _getCardTypeIcon(String cardType) {
    // Check if it's a default card type
    if (cardType == CardType.cash.name) {
      return Icons.money;
    } else if (cardType == CardType.visa.name) {
      return Icons.credit_card;
    } else if (cardType == CardType.mastercard.name) {
      return Icons.credit_card;
    } else if (cardType == CardType.americanExpress.name) {
      return Icons.credit_card;
    } else if (cardType.startsWith('custom_')) {
      return Icons.style; // Icon for custom types
    } else {
      return Icons.payment; // Default fallback
    }
  }

  Color _getCardTypeColor(String cardType) {
    // Check if it's a default card type
    if (cardType == CardType.cash.name) {
      return Colors.green;
    } else if (cardType == CardType.visa.name) {
      return Colors.blue;
    } else if (cardType == CardType.mastercard.name) {
      return Colors.red;
    } else if (cardType == CardType.americanExpress.name) {
      return Colors.purple;
    } else if (cardType.startsWith('custom_')) {
      return Colors.teal; // Color for custom types
    } else {
      return Colors.orange; // Default fallback
    }
  }

  Widget _buildTimeCounter(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    // تحديد حجم النص حسب نوع العداد
    final bool isAlarmCounter = icon == Icons.alarm;
    final double valueSize = isAlarmCounter
        ? 18.0
        : 14.0; // حجم أكبر لعداد الاستحقاق
    final double labelSize = isAlarmCounter ? 11.0 : 9.0; // حجم أكبر للتسمية

    return Flexible(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(height: 4),
          Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: valueSize,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: labelSize,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getDaysSinceEntry(Debt originalDebt) {
    final now = DateTime.now();
    final entryDate = originalDebt.entryDate;
    final difference = now.difference(entryDate);

    if (difference.inDays == 0) {
      // إذا كان اليوم، اعرض الساعات والدقائق
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours == 0) {
        return '$minutes دقيقة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    } else if (difference.inDays == 1) {
      return '1 يوم';
    } else {
      return '${difference.inDays} يوم';
    }
  }

  String _getDaysUntilDue(Debt originalDebt) {
    final now = DateTime.now();
    final dueDate = originalDebt.dueDate;
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) {
      final overdue = difference.abs();
      if (overdue == 1) {
        return 'متأخر 1';
      } else {
        return 'متأخر $overdue';
      }
    } else if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return '1';
    } else {
      return '$difference';
    }
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;

    // Convert to 12-hour format
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final period = hour < 12 ? 'صباحاً' : 'مساءً';

    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  String _formatDateArabic(DateTime date) {
    return '${date.day}\\${date.month}\\${date.year}';
  }

  void _showPaymentDialog(
    BuildContext context,
    bool initialIsFullPayment,
    Debt currentDebt,
  ) {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    double remainingAmount = currentDebt.remainingAmount;
    bool isFullPayment = initialIsFullPayment;

    if (isFullPayment) {
      amountController.text = currentDebt.remainingAmount.toStringAsFixed(2);
    }

    // Function to update remaining amount
    void updateRemainingAmount() {
      final enteredAmount = double.tryParse(amountController.text) ?? 0;
      remainingAmount = currentDebt.remainingAmount - enteredAmount;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 40,
          ),
          elevation: 16,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  isFullPayment ? Colors.green.shade50 : Colors.orange.shade50,
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with icon
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: isFullPayment
                                  ? [Colors.green, Colors.green.shade600]
                                  : [Colors.orange, Colors.orange.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    (isFullPayment
                                            ? Colors.green
                                            : Colors.orange)
                                        .withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            isFullPayment ? Icons.payment : Icons.payments,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isFullPayment ? 'تسديد كامل' : 'تسديد جزئي',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                debt.itemName, // استخدام البيانات الأصلية
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment Type Selector
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = true;
                                  amountController.text = currentDebt
                                      .remainingAmount
                                      .toStringAsFixed(2);
                                  updateRemainingAmount();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    bottomLeft: Radius.circular(12),
                                  ),
                                  gradient: isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.green,
                                            Colors.green.shade600,
                                          ],
                                        )
                                      : null,
                                  color: isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payment,
                                      color: isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد كامل',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = false;
                                  amountController.clear();
                                  updateRemainingAmount();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  ),
                                  gradient: !isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.orange,
                                            Colors.orange.shade600,
                                          ],
                                        )
                                      : null,
                                  color: !isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payments,
                                      color: !isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد جزئي',
                                      style: TextStyle(
                                        color: !isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Debt Summary Card
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ المتبقي:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(
                                  currentDebt.remainingAmount,
                                ),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                          if (!isFullPayment) ...[
                            const SizedBox(height: 12),

                            // Progress Bar
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'تقدم التسديد:',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    Text(
                                      '${((currentDebt.amount - remainingAmount) / currentDebt.amount * 100).toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 6),
                                Container(
                                  height: 8,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.grey.shade200,
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: LinearProgressIndicator(
                                      value:
                                          (currentDebt.amount -
                                              remainingAmount) /
                                          currentDebt.amount,
                                      backgroundColor: Colors.transparent,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        remainingAmount <= 0
                                            ? Colors.green
                                            : Colors.blue,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'إجمالي الدين:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    debt.amount,
                                  ), // استخدام البيانات الأصلية
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'المتبقي بعد التسديد:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: remainingAmount < 0
                                        ? Colors.red
                                        : remainingAmount == 0
                                        ? Colors.green
                                        : Colors.orange,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    remainingAmount,
                                  ),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: remainingAmount < 0
                                        ? Colors.red
                                        : remainingAmount == 0
                                        ? Colors.green
                                        : Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Amount Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: isFullPayment
                            ? Colors.grey.shade50
                            : Colors.white,
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        readOnly: isFullPayment,
                        onChanged: (value) {
                          setState(() {
                            updateRemainingAmount();
                          });
                        },
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isFullPayment
                              ? Colors.grey[600]
                              : Colors.black,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد',
                          labelStyle: TextStyle(
                            color: isFullPayment
                                ? Colors.grey[600]
                                : Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),

                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color: isFullPayment
                                ? Colors.grey[600]
                                : Colors.blue,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Date Picker
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: InkWell(
                        onTap: () async {
                          final DateTime? picked = await showCustomDatePicker(
                            context: context,
                            initialDate: selectedDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now().add(
                              const Duration(days: 30),
                            ),
                            primaryColor: isFullPayment
                                ? Colors.green
                                : Colors.orange,
                          );
                          if (picked != null && picked != selectedDate) {
                            setState(() {
                              selectedDate = picked;
                            });
                          }
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                color: isFullPayment
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'تاريخ التسديد',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.green
                                            : Colors.orange,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      DateFormat(
                                        'yyyy/MM/dd',
                                      ).format(selectedDate),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF2C3E50),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey[400],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 2,
                        style: const TextStyle(
                          color: Colors.black87, // تحديد لون النص بوضوح
                          fontSize: 14,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () async {
                              final amount = double.tryParse(
                                amountController.text,
                              );
                              if (amount == null || amount <= 0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(Icons.error, color: Colors.white),
                                        SizedBox(width: 8),
                                        Text('يرجى إدخال مبلغ صحيح'),
                                      ],
                                    ),
                                    backgroundColor: Colors.red,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                                return;
                              }

                              if (amount > currentDebt.remainingAmount) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(
                                          Icons.warning,
                                          color: Colors.white,
                                        ),
                                        SizedBox(width: 8),
                                        Text('المبلغ أكبر من المبلغ المتبقي'),
                                      ],
                                    ),
                                    backgroundColor: Colors.orange,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                                return;
                              }

                              try {
                                final debtProvider = Provider.of<DebtProvider>(
                                  context,
                                  listen: false,
                                );

                                await debtProvider.makePayment(
                                  currentDebt.id!,
                                  amount,
                                  isFullPayment
                                      ? PaymentType.full
                                      : PaymentType.partial,
                                  notesController.text.trim().isEmpty
                                      ? null
                                      : notesController.text.trim(),
                                  paymentDate: selectedDate,
                                );

                                if (context.mounted) {
                                  Navigator.pop(context);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(
                                            Icons.check_circle,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            isFullPayment
                                                ? 'تم التسديد الكامل بنجاح'
                                                : 'تم التسديد الجزئي بنجاح',
                                          ),
                                        ],
                                      ),
                                      backgroundColor: Colors.green,
                                      behavior: SnackBarBehavior.floating,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  );
                                }
                              } catch (e) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(
                                            Icons.error,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'حدث خطأ: ${e.toString()}',
                                            ),
                                          ),
                                        ],
                                      ),
                                      backgroundColor: Colors.red,
                                      behavior: SnackBarBehavior.floating,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  );
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isFullPayment
                                  ? Colors.green
                                  : Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 4,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  isFullPayment
                                      ? Icons.payment
                                      : Icons.payments,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  isFullPayment ? 'تسديد كامل' : 'تسديد جزئي',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _editDebt(BuildContext context, Debt currentDebt) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddDebtScreen(customer: customer, debt: currentDebt),
      ),
    );
  }

  void _deleteDebt(BuildContext context, Debt currentDebt) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_forever,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'حذف الدين',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف الدين "${currentDebt.itemName}"؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          await Provider.of<DebtProvider>(
                            context,
                            listen: false,
                          ).deleteDebt(currentDebt.id!);

                          if (context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم حذف الدين بنجاح'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('حدث خطأ: ${e.toString()}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف الدين',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تحديد لون البطاقة حسب تاريخ البيع
  Color _getSaleStatusColor(Debt originalDebt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final entryDate = DateTime(
      originalDebt.entryDate.year,
      originalDebt.entryDate.month,
      originalDebt.entryDate.day,
    );

    if (entryDate.isAtSameMomentAs(today)) {
      return const Color(0xFF4CAF50); // اليوم - أخضر زاهي
    } else if (entryDate.isAtSameMomentAs(yesterday)) {
      return const Color(0xFF1A237E); // أمس - أزرق غامق مائل للأسود
    } else if (entryDate.isAfter(yesterday)) {
      return const Color(0xFF2196F3); // خلال آخر يومين - أزرق زاهي
    } else {
      return const Color(0xFF9E9E9E); // أقدم من ذلك - رمادي
    }
  }
}
