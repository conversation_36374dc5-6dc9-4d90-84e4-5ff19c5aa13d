import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../database/database_helper.dart';
import '../utils/number_formatter.dart';
import '../providers/card_type_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../models/custom_card_type.dart';
import '../models/card_inventory.dart';
import 'dart:math' as math;

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // تحديث البيانات
  void _refreshData() {
    setState(() {});
  }

  // جلب إحصائيات اليوم والأمس
  Future<Map<String, dynamic>> _getDailySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    final todayDebts = allDebts.where((debt) {
      final debtDay = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      return debtDay.isAtSameMomentAs(today);
    }).toList();

    final yesterdayDebts = allDebts.where((debt) {
      final debtDay = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      return debtDay.isAtSameMomentAs(yesterday);
    }).toList();

    return {
      'today': {
        'count': todayDebts.length,
        'amount': todayDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
      },
      'yesterday': {
        'count': yesterdayDebts.length,
        'amount': yesterdayDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        ),
      },
    };
  }

  // جلب إحصائيات الأسبوع
  Future<Map<String, dynamic>> _getWeeklySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDay = DateTime(
      weekStart.year,
      weekStart.month,
      weekStart.day,
    );

    final weekDebts = allDebts.where((debt) {
      return debt.entryDate.isAfter(
            weekStartDay.subtract(const Duration(days: 1)),
          ) &&
          debt.entryDate.isBefore(now.add(const Duration(days: 1)));
    }).toList();

    return {
      'count': weekDebts.length,
      'amount': weekDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
    };
  }

  // جلب إحصائيات الشهر
  Future<Map<String, dynamic>> _getMonthlySalesStats() async {
    final allDebts = await _databaseHelper.getAllDebts();
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month);

    final monthDebts = allDebts.where((debt) {
      return debt.entryDate.isAfter(
            monthStart.subtract(const Duration(days: 1)),
          ) &&
          debt.entryDate.isBefore(now.add(const Duration(days: 1)));
    }).toList();

    return {
      'count': monthDebts.length,
      'amount': monthDebts.fold<double>(0, (sum, debt) => sum + debt.amount),
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'الإحصائيات المتكاملة',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'عامة'),
            Tab(icon: Icon(Icons.credit_card), text: 'المبيعات'),
            Tab(icon: Icon(Icons.inventory), text: 'المخزون'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralStatistics(),
          _buildSalesStatistics(),
          _buildStockStatistics(),
        ],
      ),
    );
  }

  // الإحصائيات العامة
  Widget _buildGeneralStatistics() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _databaseHelper.getStatistics(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final statistics = snapshot.data ?? {};
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildOverviewCards(statistics),
            const SizedBox(height: 24),
            _buildFinancialCards(statistics),
            const SizedBox(height: 24),
            _buildStatusCards(statistics),
          ],
        );
      },
    );
  }

  // إحصائيات المبيعات
  Widget _buildSalesStatistics() {
    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        _databaseHelper.getSalesByCardType(),
        _getDailySalesStats(),
        _getWeeklySalesStats(),
        _getMonthlySalesStats(),
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final results = snapshot.data ?? [];
        final salesData = results[0] as List<Map<String, dynamic>>? ?? [];
        final todayStats = results[1] as Map<String, dynamic>? ?? {};
        final weekStats = results[2] as Map<String, dynamic>? ?? {};
        final monthStats = results[3] as Map<String, dynamic>? ?? {};

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildDailySalesCards(todayStats),
            const SizedBox(height: 16),
            _buildPeriodSalesCards(weekStats, monthStats),
            const SizedBox(height: 20),
            _buildSalesOverview(salesData),
            const SizedBox(height: 20),
            _buildSalesChart(salesData),
            const SizedBox(height: 24),
            _buildCardTypeSalesAnalysis(salesData),
            const SizedBox(height: 24),
            _buildSalesDistributionChart(salesData),
          ],
        );
      },
    );
  }

  // إحصائيات المخزون
  Widget _buildStockStatistics() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _databaseHelper.getStockStatistics(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorWidget();
        }

        final stockData = snapshot.data ?? <String, dynamic>{};

        return Consumer2<CardInventoryProvider, CardTypeProvider>(
          builder: (context, inventoryProvider, cardTypeProvider, child) {
            // تم إلغاء التحديث التلقائي للمخزون مؤقتاً

            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // إحصائيات المخزون التقليدية
                _buildStockOverview(stockData),
                const SizedBox(height: 16),

                // توزيع المخزون مباشرة تحت الملخص
                Consumer2<CardInventoryProvider, CardTypeProvider>(
                  builder: (context, inventoryProvider, cardTypeProvider, _) {
                    if (inventoryProvider.isLoading) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    } else if (inventoryProvider.inventories.isNotEmpty) {
                      return _buildInventoryDistributionChart(
                        inventoryProvider,
                        cardTypeProvider,
                      );
                    } else {
                      return _buildEmptyStockState();
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // حالة المخزون الفارغ
  Widget _buildEmptyStockState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بطاقات في المخزون',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اذهب إلى شاشة المخزون لإضافة بطاقات',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // ويدجت الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 18, color: Colors.red.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى المحاولة مرة أخرى',
            style: TextStyle(color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  // بطاقات المبيعات اليومية
  Widget _buildDailySalesCards(Map<String, dynamic> todayStats) {
    final todayData = todayStats['today'] ?? {};
    final yesterdayData = todayStats['yesterday'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مبيعات اليوم والأمس',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildCompactStatCard(
                'اليوم',
                '${todayData['count'] ?? 0}',
                'كارت',
                NumberFormatter.formatCurrency(
                  (todayData['amount'] ?? 0).toInt(),
                ),
                Icons.today,
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatCard(
                'الأمس',
                '${yesterdayData['count'] ?? 0}',
                'كارت',
                NumberFormatter.formatCurrency(
                  (yesterdayData['amount'] ?? 0).toInt(),
                ),
                Icons.history,
                Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقات المبيعات الأسبوعية والشهرية
  Widget _buildPeriodSalesCards(
    Map<String, dynamic> weekStats,
    Map<String, dynamic> monthStats,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مبيعات الأسبوع والشهر',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildCompactStatCard(
                'الأسبوع',
                '${weekStats['count'] ?? 0}',
                'كارت',
                NumberFormatter.formatCurrency(
                  (weekStats['amount'] ?? 0).toInt(),
                ),
                Icons.date_range,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatCard(
                'الشهر',
                '${monthStats['count'] ?? 0}',
                'كارت',
                NumberFormatter.formatCurrency(
                  (monthStats['amount'] ?? 0).toInt(),
                ),
                Icons.calendar_month,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقة إحصائية مدمجة وصغيرة
  Widget _buildCompactStatCard(
    String title,
    String count,
    String unit,
    String amount,
    IconData icon,
    MaterialColor color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color.shade600, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '$count $unit',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // بطاقات النظرة العامة
  Widget _buildOverviewCards(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نظرة عامة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي العملاء',
                NumberFormatter.formatNumber(statistics['totalCustomers'] ?? 0),
                Icons.people,
                Colors.blue,
                'عميل',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'الديون المتأخرة',
                NumberFormatter.formatNumber(statistics['overdueDebts'] ?? 0),
                Icons.warning,
                Colors.red,
                'دين',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // البطاقات المالية
  Widget _buildFinancialCards(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الملخص المالي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الديون',
                NumberFormatter.formatCurrency(statistics['totalDebts'] ?? 0),
                Icons.account_balance,
                Colors.orange,
                'د.ع',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي المدفوعات',
                NumberFormatter.formatCurrency(statistics['totalPaid'] ?? 0),
                Icons.payment,
                Colors.green,
                'د.ع',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقات الحالة
  Widget _buildStatusCards(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'حالة الديون',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الديون المدفوعة',
                NumberFormatter.formatNumber(statistics['paidDebts'] ?? 0),
                Icons.check_circle,
                Colors.green,
                'دين',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'الديون المعلقة',
                NumberFormatter.formatNumber(statistics['pendingDebts'] ?? 0),
                Icons.pending,
                Colors.amber,
                'دين',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const Spacer(),
              Text(
                unit,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // نظرة عامة على المبيعات
  Widget _buildSalesOverview(List<Map<String, dynamic>> salesData) {
    int totalSales = 0;
    int totalQuantity = 0;
    double totalAmount = 0;

    for (var sale in salesData) {
      totalSales += sale['count'] as int? ?? 0;
      totalQuantity += sale['total_quantity'] as int? ?? 0;
      totalAmount += (sale['total_amount'] as num? ?? 0).toDouble();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ملخص المبيعات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المبيعات',
                NumberFormatter.formatNumber(totalSales),
                Icons.shopping_cart,
                Colors.blue,
                'عملية',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي الكمية',
                NumberFormatter.formatNumber(totalQuantity),
                Icons.inventory_2,
                Colors.green,
                'بطاقة',
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'إجمالي المبلغ',
          NumberFormatter.formatCurrency(totalAmount.toInt()),
          Icons.attach_money,
          Colors.orange,
          'د.ع',
        ),
      ],
    );
  }

  // مبيعات حسب نوع الكارت
  Widget _buildCardTypeSales(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات مبيعات'));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المبيعات حسب نوع البطاقة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...salesData.map((sale) => _buildCardTypeSaleItem(sale)),
      ],
    );
  }

  // عنصر مبيعات نوع البطاقة
  Widget _buildCardTypeSaleItem(Map<String, dynamic> sale) {
    final cardType = sale['card_type'] as String? ?? 'غير محدد';
    final count = sale['count'] as int? ?? 0;
    final totalQuantity = sale['total_quantity'] as int? ?? 0;
    final totalAmount = (sale['total_amount'] as num? ?? 0).toDouble();

    final Color cardColor = _getCardTypeColor(cardType);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: cardColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: cardColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.credit_card, color: cardColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getCardTypeDisplayName(cardType),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$count عملية • $totalQuantity بطاقة',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                NumberFormatter.formatCurrency(totalAmount.toInt()),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                ),
              ),
              Text(
                'د.ع',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // رسم بياني للمبيعات
  Widget _buildSalesChart(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات للرسم البياني'));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مخطط المبيعات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87, // لون أسود ثابت للعنوان لضمان الوضوح
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildSimpleBarChart(salesData),
        ),
      ],
    );
  }

  // رسم بياني بسيط
  Widget _buildSimpleBarChart(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) return const SizedBox();

    final maxAmount = salesData
        .map((e) => (e['total_amount'] as num? ?? 0).toDouble())
        .reduce(math.max);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: salesData.map((sale) {
        final amount = (sale['total_amount'] as num? ?? 0).toDouble();
        final cardType = sale['card_type'] as String? ?? '';
        final height = maxAmount > 0 ? (amount / maxAmount) * 150 : 0.0;
        final color = _getCardTypeColor(cardType);

        return Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              NumberFormatter.formatCurrency(amount.toInt()),
              style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Container(
              width: 40,
              height: height,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getCardTypeDisplayName(cardType),
              style: const TextStyle(
                fontSize: 10,
                color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      }).toList(),
    );
  }

  // نظرة عامة على المخزون
  Widget _buildStockOverview(Map<String, dynamic> stockData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ملخص المخزون',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'أنواع البطاقات',
                NumberFormatter.formatNumber(stockData['totalCardTypes'] ?? 0),
                Icons.category,
                Colors.blue,
                'نوع',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي الكمية',
                NumberFormatter.formatNumber(stockData['totalQuantity'] ?? 0),
                Icons.inventory,
                Colors.green,
                'بطاقة',
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'مخزون منخفض',
                NumberFormatter.formatNumber(stockData['lowStockCount'] ?? 0),
                Icons.warning,
                Colors.orange,
                'نوع',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'مخزون نافد',
                NumberFormatter.formatNumber(stockData['outOfStockCount'] ?? 0),
                Icons.error,
                Colors.red,
                'نوع',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // الحصول على لون نوع البطاقة
  Color _getCardTypeColor(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو الستة':
        return Colors.grey;
      case 'أبو العشرة':
        return Colors.teal;
      default:
        return Colors.blue;
    }
  }

  // الحصول على اسم عرض نوع البطاقة
  String _getCardTypeDisplayName(String cardType) {
    // التحقق من أن cardType ليس فارغ أو null
    if (cardType.isEmpty) {
      return 'غير محدد';
    }

    // استخدام CardTypeProvider للحصول على الاسم الصحيح
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // أولاً، نحاول البحث بالمعرف المباشر
    CardTypeOption? cardTypeOption = cardTypeProvider.getCardTypeById(cardType);

    // إذا لم نجد، نحاول البحث في الأنواع الافتراضية بطرق مختلفة
    if (cardTypeOption == null) {
      try {
        cardTypeOption = cardTypeProvider.allCardTypes
            .cast<CardTypeOption?>()
            .firstWhere(
              (type) =>
                  type != null &&
                  (type.id.toLowerCase() == cardType.toLowerCase() ||
                      type.defaultType?.name.toLowerCase() ==
                          cardType.toLowerCase() ||
                      (type.defaultType != null &&
                          _compareCardTypes(type.defaultType!.name, cardType))),
              orElse: () => null,
            );
      } catch (e) {
        // في حالة حدوث خطأ، نستخدم التحويل اليدوي
        cardTypeOption = null;
      }
    }

    if (cardTypeOption != null && cardTypeOption.displayName.isNotEmpty) {
      return cardTypeOption.displayName;
    }

    // إذا لم نجد النوع، نحاول التحويل اليدوي
    final convertedName = _convertCardTypeToArabic(cardType);
    return convertedName.isNotEmpty ? convertedName : 'غير محدد';
  }

  // مقارنة أنواع البطاقات مع مراعاة الاختلافات
  bool _compareCardTypes(String enumName, String dbValue) {
    final cleanEnum = enumName.toLowerCase();
    final cleanDb = dbValue.toLowerCase();

    // مقارنات خاصة للأنواع المختلفة
    if ((cleanEnum == 'sia' && (cleanDb == 'asia' || cleanDb == 'آسيا')) ||
        (cleanEnum == 'asia' && (cleanDb == 'sia' || cleanDb == 'آسيا')) ||
        (cleanDb == 'sia' && cleanEnum == 'asia') ||
        (cleanDb == 'asia' && cleanEnum == 'sia')) {
      return true;
    }

    if ((cleanEnum == 'abuashara' &&
            (cleanDb.contains('ashara') || cleanDb.contains('عشرة'))) ||
        (cleanEnum == 'abusitta' &&
            (cleanDb.contains('sitta') || cleanDb.contains('ستة')))) {
      return true;
    }

    return cleanEnum == cleanDb;
  }

  // تحويل أسماء البطاقات إلى عربية
  String _convertCardTypeToArabic(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    final cleanType = cardType.trim().toLowerCase();

    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia': // إضافة دعم لـ asia أيضاً
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      // إضافة المزيد من الأنواع الشائعة
      case 'custom_zain':
        return 'زين';
      case 'custom_asia':
      case 'custom_sia':
        return 'آسيا';
      case 'custom_abuashara':
        return 'أبو العشرة';
      case 'custom_abusitta':
        return 'أبو الستة';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.trim();
        }
        // إذا كان يبدأ بـ custom_ نحاول استخراج الاسم
        if (cleanType.startsWith('custom_')) {
          final extractedName = cleanType.substring(7); // إزالة 'custom_'
          return _convertCardTypeToArabic(extractedName);
        }
        // إذا كان إنجليزي ولم نجده، نعيد الاسم الأصلي مع تحسين التنسيق
        return _capitalizeFirst(cardType.trim());
    }
  }

  // تحسين تنسيق النص
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // فحص إذا كان النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  // تحليل مبيعات أنواع البطاقات
  Widget _buildCardTypeSalesAnalysis(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return _buildEmptyAnalysisCard();
    }

    // حساب إجمالي المبيعات
    final totalAmount = salesData.fold<double>(
      0.0,
      (sum, sale) => sum + ((sale['total_amount'] as num?) ?? 0).toDouble(),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue.shade600, size: 24),
            const SizedBox(width: 8),
            const Text(
              'تحليل المبيعات حسب نوع البطاقة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // بطاقات المبيعات
        ...salesData
            .take(4)
            .map((sale) => _buildSalesAnalysisCard(sale, totalAmount)),

        if (salesData.length > 4)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.more_horiz, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  'و ${salesData.length - 4} أنواع أخرى',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // بطاقة تحليل مبيعات
  Widget _buildSalesAnalysisCard(
    Map<String, dynamic> sale,
    double totalAmount,
  ) {
    final cardType = sale['card_type'] as String? ?? 'غير محدد';
    final count = sale['count'] as int? ?? 0;
    final quantity = sale['total_quantity'] as int? ?? 0;
    final amount = (sale['total_amount'] as num? ?? 0).toDouble();
    final percentage = totalAmount > 0 ? (amount / totalAmount) * 100 : 0.0;

    final cardColor = _getCardTypeColor(cardType);
    final displayName = _getCardTypeDisplayName(cardType);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: cardColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: cardColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          // Header مع اسم البطاقة والنسبة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cardColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.credit_card, color: cardColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    displayName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // المحتوى مع الإحصائيات
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // العمليات
                Expanded(
                  child: _buildMiniStatItem(
                    'العمليات',
                    count.toString(),
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // الكمية
                Expanded(
                  child: _buildMiniStatItem(
                    'الكمية',
                    quantity.toString(),
                    Icons.inventory_2,
                    Colors.green,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey.shade200),
                // المبلغ
                Expanded(
                  child: _buildMiniStatItem(
                    'المبلغ',
                    NumberFormatter.formatCurrency(amount.toInt()),
                    Icons.attach_money,
                    cardColor,
                  ),
                ),
              ],
            ),
          ),

          // شريط التقدم
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: cardColor,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // عنصر إحصائية صغير
  Widget _buildMiniStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  // رسم توزيع المبيعات - أفقي مع النسب المئوية
  Widget _buildSalesDistributionChart(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return _buildEmptyAnalysisCard();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.bar_chart, color: Colors.purple.shade600, size: 24),
            const SizedBox(width: 8),
            const Text(
              'توزيع المبيعات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildSalesHorizontalBarChart(salesData),
        ),
      ],
    );
  }

  // مخطط أعمدة المبيعات الأفقي مع النسب المئوية
  Widget _buildSalesHorizontalBarChart(List<Map<String, dynamic>> salesData) {
    if (salesData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات مبيعات',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // حساب إجمالي المبيعات للنسب المئوية
    final totalAmount = salesData.fold<double>(
      0.0,
      (sum, sale) => sum + ((sale['total_amount'] as num?) ?? 0).toDouble(),
    );

    // العثور على أعلى مبلغ للشريط
    final maxAmount = salesData.fold<double>(0.0, (max, sale) {
      final amount = (sale['total_amount'] as num? ?? 0).toDouble();
      return amount > max ? amount : max;
    });

    if (totalAmount == 0) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.warning_outlined,
              size: 48,
              color: Colors.orange.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مبيعات',
              style: TextStyle(
                fontSize: 16,
                color: Colors.orange.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: salesData.map((sale) {
        final cardType = sale['card_type'] as String? ?? 'غير محدد';
        final count = sale['count'] as int? ?? 0;
        final quantity = sale['total_quantity'] as int? ?? 0;
        final amount = (sale['total_amount'] as num? ?? 0).toDouble();
        final percentage = totalAmount > 0 ? (amount / totalAmount) * 100 : 0.0;
        final barPercentage = maxAmount > 0 ? amount / maxAmount : 0.0;

        final cardColor = _getCardTypeColor(cardType);
        final displayName = _getCardTypeDisplayName(cardType);

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: cardColor.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: cardColor.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف العلوي: اسم البطاقة والنسبة المئوية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: cardColor,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            displayName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // الصف الثاني: المبلغ والشريط الأفقي
              Row(
                children: [
                  // المبلغ
                  SizedBox(
                    width: 100,
                    child: Text(
                      NumberFormatter.formatCurrency(amount.toInt()),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: cardColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // الشريط الأفقي
                  Expanded(
                    child: Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: barPercentage,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: LinearGradient(
                              colors: [
                                cardColor.withValues(alpha: 0.7),
                                cardColor,
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // عدد العمليات والكمية
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '$count عملية',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '$quantity بطاقة',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // بطاقة فارغة للتحليل
  Widget _buildEmptyAnalysisCard() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(Icons.analytics_outlined, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات مبيعات',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة ديون لرؤية التحليل',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // مخطط توزيع البطاقات الجديد
  Widget _buildInventoryDistributionChart(
    CardInventoryProvider inventoryProvider,
    CardTypeProvider cardTypeProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.bar_chart, color: Colors.green.shade600, size: 24),
            const SizedBox(width: 8),
            const Text(
              'توزيع المخزون',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildInventoryBarChart(inventoryProvider, cardTypeProvider),
        ),
      ],
    );
  }

  // مخطط أعمدة المخزون الجديد - أفقي واحترافي مع النسب المئوية
  Widget _buildInventoryBarChart(
    CardInventoryProvider inventoryProvider,
    CardTypeProvider cardTypeProvider,
  ) {
    if (inventoryProvider.inventories.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات لعرضها',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    final maxQuantity = inventoryProvider.inventories
        .map((s) => s.quantity)
        .reduce((a, b) => a > b ? a : b);

    // حساب إجمالي الكمية للنسب المئوية
    final totalQuantity = inventoryProvider.inventories.fold<int>(
      0,
      (sum, inventory) => sum + inventory.quantity,
    );

    if (maxQuantity == 0) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.warning_outlined,
              size: 48,
              color: Colors.orange.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'جميع البطاقات نافدة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.orange.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: inventoryProvider.inventories.map((inventory) {
        final cardName = _getCardTypeDisplayName(inventory.cardType);
        final percentage = inventory.quantity / maxQuantity; // للشريط
        final percentageOfTotal = totalQuantity > 0
            ? (inventory.quantity / totalQuantity) * 100
            : 0.0; // للنسبة المئوية
        final color = _getInventoryCardColor(inventory);

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف العلوي: اسم البطاقة والنسبة المئوية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            cardName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${percentageOfTotal.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // الصف الثاني: الكمية والشريط الأفقي
              Row(
                children: [
                  // الكمية
                  SizedBox(
                    width: 80,
                    child: Text(
                      '${inventory.quantity} بطاقة',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // الشريط الأفقي
                  Expanded(
                    child: Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: LinearGradient(
                              colors: [color.withValues(alpha: 0.7), color],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // القيمة القصوى
                  Text(
                    '$maxQuantity',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // الحصول على لون بطاقة المخزون الجديد
  Color _getInventoryCardColor(CardInventory inventory) {
    if (inventory.quantity == 0) return Colors.red;
    if (inventory.isLowStock) return Colors.orange;
    return Colors.green;
  }
}
