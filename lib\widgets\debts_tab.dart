import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../providers/debt_provider.dart';
import '../widgets/debt_card.dart';
import '../utils/number_formatter.dart';

class DebtsTab extends StatefulWidget {
  const DebtsTab({super.key, required this.customer});
  final Customer customer;

  @override
  State<DebtsTab> createState() => _DebtsTabState();
}

class _DebtsTabState extends State<DebtsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isMultiSelectMode = false;
  final Set<int> _selectedDebtIds = {};

  @override
  void initState() {
    super.initState();
    // Ensure debts are loaded when tab is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      if (debtProvider.currentCustomerId != widget.customer.id) {
        debtProvider.loadCustomerDebts(widget.customer.id!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final activeDebts = debtProvider.debts
            .where((debt) => debt.status != DebtStatus.paid)
            .toList();

        if (activeDebts.isEmpty) {
          return RefreshIndicator(
            onRefresh: () async {
              await debtProvider.loadCustomerDebts(widget.customer.id!);
            },
            child: ListView(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.6,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 80,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد ديون',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'اضغط على زر + لإضافة دين جديد',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'اسحب للأسفل للتحديث',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[400],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Top Bar with Menu
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  if (_isMultiSelectMode) ...[
                    // Selection Mode Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تم تحديد ${_selectedDebtIds.length} دين',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            'المبلغ الإجمالي: ${NumberFormatter.formatCurrency(_calculateSelectedDebtsTotal(activeDebts))}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isMultiSelectMode = false;
                          _selectedDebtIds.clear();
                        });
                      },
                      icon: const Icon(Icons.close),
                      tooltip: 'إلغاء التحديد',
                    ),
                  ] else ...[
                    // Normal Mode
                    const Expanded(
                      child: Text(
                        'قائمة الديون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert),
                      onSelected: (value) {
                        if (value == 'select_all') {
                          setState(() {
                            _isMultiSelectMode = true;
                            _selectedDebtIds.clear();
                            for (final debt in activeDebts) {
                              if (debt.id != null) {
                                _selectedDebtIds.add(debt.id!);
                              }
                            }
                          });
                        } else if (value == 'pay_all') {
                          setState(() {
                            _isMultiSelectMode = true;
                            _selectedDebtIds.clear();
                            for (final debt in activeDebts) {
                              if (debt.id != null) {
                                _selectedDebtIds.add(debt.id!);
                              }
                            }
                          });
                          _showPaySelectedDebtsDialog(
                            context,
                            debtProvider,
                            activeDebts,
                          );
                        } else if (value == 'delete_all') {
                          setState(() {
                            _isMultiSelectMode = true;
                            _selectedDebtIds.clear();
                            for (final debt in activeDebts) {
                              if (debt.id != null) {
                                _selectedDebtIds.add(debt.id!);
                              }
                            }
                          });
                          _deleteSelectedDebts(context, debtProvider);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'select_all',
                          child: Row(
                            children: [
                              Icon(Icons.select_all, color: Colors.blue),
                              SizedBox(width: 12),
                              Text('تحديد الكل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'pay_all',
                          child: Row(
                            children: [
                              Icon(Icons.payment, color: Colors.green),
                              SizedBox(width: 12),
                              Text('تسديد الكل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete_all',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 12),
                              Text('حذف الكل'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // Selection Actions (only shown when in selection mode)
            if (_isMultiSelectMode)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border(
                    bottom: BorderSide(color: Colors.blue.shade200, width: 0.5),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedDebtIds.isNotEmpty
                            ? () => _showPaySelectedDebtsDialog(
                                context,
                                debtProvider,
                                activeDebts,
                              )
                            : null,
                        icon: const Icon(Icons.payment, size: 18),
                        label: const Text('تسديد المحدد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedDebtIds.isNotEmpty
                            ? () => _deleteSelectedDebts(context, debtProvider)
                            : null,
                        icon: const Icon(Icons.delete, size: 18),
                        label: const Text('حذف المحدد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Debts List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerDebts(widget.customer.id!);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  itemCount: activeDebts.length,
                  itemBuilder: (context, index) {
                    final debt = activeDebts[index];

                    return GestureDetector(
                      onLongPress: () {
                        setState(() {
                          _isMultiSelectMode = true;
                          if (debt.id != null) {
                            _selectedDebtIds.add(debt.id!);
                          }
                        });
                      },
                      onTap: _isMultiSelectMode
                          ? () {
                              setState(() {
                                if (debt.id != null) {
                                  if (_selectedDebtIds.contains(debt.id!)) {
                                    _selectedDebtIds.remove(debt.id!);
                                    if (_selectedDebtIds.isEmpty) {
                                      _isMultiSelectMode = false;
                                    }
                                  } else {
                                    _selectedDebtIds.add(debt.id!);
                                  }
                                }
                              });
                            }
                          : null,
                      child: Stack(
                        children: [
                          DebtCard(debt: debt, customer: widget.customer),
                          if (_isMultiSelectMode)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: _selectedDebtIds.contains(debt.id)
                                      ? Colors.blue
                                      : Colors.white,
                                  border: Border.all(
                                    color: _selectedDebtIds.contains(debt.id)
                                        ? Colors.blue
                                        : Colors.grey,
                                    width: 2,
                                  ),
                                ),
                                child: _selectedDebtIds.contains(debt.id)
                                    ? const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 16,
                                      )
                                    : null,
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Calculate total amount of selected debts
  double _calculateSelectedDebtsTotal(List<Debt> activeDebts) {
    double total = 0.0;
    for (final debt in activeDebts) {
      if (_selectedDebtIds.contains(debt.id)) {
        total += debt.remainingAmount;
      }
    }
    return total;
  }

  // Delete selected debts
  Future<void> _deleteSelectedDebts(
    BuildContext context,
    DebtProvider debtProvider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_forever,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'تأكيد حذف المحدد',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف ${_selectedDebtIds.length} دين محدد؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context, false),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context, true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف المحدد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (confirmed == true) {
      try {
        for (final debtId in _selectedDebtIds) {
          await debtProvider.deleteDebt(debtId);
        }

        setState(() {
          _selectedDebtIds.clear();
          _isMultiSelectMode = false;
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الديون: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Show payment dialog for selected debts
  Future<void> _showPaySelectedDebtsDialog(
    BuildContext context,
    DebtProvider debtProvider,
    List<Debt> activeDebts,
  ) async {
    final selectedDebts = activeDebts
        .where((debt) => _selectedDebtIds.contains(debt.id))
        .toList();

    final totalAmount = _calculateSelectedDebtsTotal(activeDebts);
    bool isFullPayment = true;
    final amountController = TextEditingController(
      text: totalAmount.toStringAsFixed(2),
    );
    final notesController = TextEditingController();
    final DateTime selectedDate = DateTime.now();

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 40,
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.green.shade50],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.green, Colors.green.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.payment,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تسديد الديون المحددة',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                '${selectedDebts.length} دين محدد',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context, false),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment Type Selector
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = true;
                                  amountController.text = totalAmount
                                      .toStringAsFixed(2);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    bottomLeft: Radius.circular(12),
                                  ),
                                  gradient: isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.green,
                                            Colors.green.shade600,
                                          ],
                                        )
                                      : null,
                                  color: isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payment,
                                      color: isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد كامل',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = false;
                                  amountController.clear();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  ),
                                  gradient: !isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.orange,
                                            Colors.orange.shade600,
                                          ],
                                        )
                                      : null,
                                  color: !isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payments,
                                      color: !isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد جزئي',
                                      style: TextStyle(
                                        color: !isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Summary Card
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ الإجمالي:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(totalAmount),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Amount Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: isFullPayment
                            ? Colors.grey.shade50
                            : Colors.white,
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        readOnly: isFullPayment,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isFullPayment
                              ? Colors.grey[600]
                              : Colors.black,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد',
                          labelStyle: TextStyle(
                            color: isFullPayment
                                ? Colors.grey[600]
                                : Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color: isFullPayment
                                ? Colors.grey[600]
                                : Colors.blue,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 2,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context, false),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context, true),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'تأكيد التسديد',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    if (confirmed == true) {
      final amount = double.tryParse(amountController.text);
      if (amount == null || amount <= 0) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى إدخال مبلغ صحيح'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      try {
        // Pay selected debts
        for (final debt in selectedDebts) {
          final paymentAmount = isFullPayment
              ? debt.remainingAmount
              : (amount * debt.remainingAmount / totalAmount);

          await debtProvider.makePayment(
            debt.id!,
            paymentAmount,
            paymentAmount >= debt.remainingAmount
                ? PaymentType.full
                : PaymentType.partial,
            notesController.text.trim().isEmpty
                ? null
                : notesController.text.trim(),
            paymentDate: selectedDate,
          );
        }

        setState(() {
          _selectedDebtIds.clear();
          _isMultiSelectMode = false;
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسديد الديون المحددة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في التسديد: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
